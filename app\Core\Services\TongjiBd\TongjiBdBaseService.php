<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/17 下午2:20
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\TongjiBd;

use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\User\User;
use App\Model\User\UserDepartment;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Hyperf\Guzzle\ClientFactory;

class TongjiBdBaseService
{
    /**
     * @var \Hyperf\Guzzle\ClientFactory
     */
    private $clientFactory;

    public function __construct(ClientFactory $clientFactory)
    {
        $options = [
            'base_uri' => 'https://api.baidu.com/json/tongji/v1/'
        ];
        $this->clientFactory = $clientFactory->create($options);
    }

    public function sendRequest($uri, $body, $method = 'get')
    {
        try {
            $options['header'] = [
                'username'     => env('TONGJI_BAIDU_USERNAME'),
                'password'     => env('TONGJI_BAIDU_PASSWORD'),
                'token'        => env('TONGJI_BAIDU_TOKEN'),
                'account_type' => 1
            ];
            if($body){
                $options['body'] = $body;
            }
            $response = $this->clientFactory->request($method, $uri, [
                'json'  => $options
            ]);

            $responseArr = json_decode($response->getBody()->getContents(), true);

            if($responseArr['header']['succ'] === 0){
                throw new AppException(StatusCode::ERR_EXCEPTION, json_encode($responseArr['header']['failures']));
            }
            return $responseArr['body'];
        } catch (GuzzleException $e) {
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }
}