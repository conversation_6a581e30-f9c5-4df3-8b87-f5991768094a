<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/9/8 上午11:54
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing\StationPC;

use App\Core\Services\Marketing\AppReportService;
use App\Core\Services\Marketing\DeviceReportService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController(prefix="/marketing/stationpc/device")
 */
class DeviceController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var DeviceReportService
     */
    protected $service;

    public function statResult()
    {
        $result = $this->service->statResult();
        return $this->response->success($result);
    }

    public function getPanelData()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getPanelData($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function getTrendData()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTrendData($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function getTableData()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTableData($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }
}