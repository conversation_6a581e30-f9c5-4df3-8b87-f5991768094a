<?php
/**
 * @Copyright T-chip Team.
 * @Date      2023/3/20 下午6:03
 * <AUTHOR> X
 * @Description
 */

namespace App\Constants;

class FollowUpSource extends eConstants
{
    /* 中文论坛 */
    const Firefly_bbscn = 1;

    /* 英文论坛 */
    const Firefly_bbsen = 2;

    public static function text($value)
    {
        return self::info($value)['text'] ?? '未定义的来源';
    }

    public static function platform()
    {
        return array_column(self::info(), 'platform');
    }

    public static function info($source = null)
    {
        $data = [
            FollowUpSource::Firefly_bbscn => [

                'id'     => FollowUpSource::Firefly_bbscn,
                'text'     => 'Firefly 中文论坛',
                'host'     => env('FIREFLY_BBSCN_URL'),
                'platform' => 'firefly_bbscn',

            ],
            FollowUpSource::Firefly_bbsen => [
                'id'     => FollowUpSource::Firefly_bbsen,
                'text'     => 'Firefly 英文论坛',
                'host'     => env('FIREFLY_BBSEN_URL'),
                'platform' => 'firefly_bbsen',

            ]
        ];
        if($source){
            return  $data[$source];
        }
        return $data;
    }


    public static function getInfoByPlatform($platform){
        $data=self::info();
        foreach ($data as $val){
            if($val['platform']==$platform){
                return $val;
            }
        }
        return [];
    }
    public static function getPlatformBySource($source){
        $data=self::info($source);
        return $data['platform']??'';
    }

}
