<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/18 下午2:18
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\MpWx;

use App\Core\Services\MpWx\MpWxBaseService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class IndexController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MpWxBaseService
     */
    protected $mpWxBaseService;

    public function getToken()
    {
        $result = $this->mpWxBaseService->getToken();
        return $this->response->success($result);
    }
}