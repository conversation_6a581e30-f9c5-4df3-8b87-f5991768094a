<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing;

use App\Core\Services\Marketing\MarketingPlatformMonthReportService;
use App\Request\Marketing\BrandCodeDateRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class MarketingPlatformMonthReportController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MarketingPlatformMonthReportService
     */
    protected $service;

    public function brandStatisticsSales(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $productName = $this->request->input('product_name');
        return $this->response->success($this->service->brandStatisticsSales($validated['brand_code'], $validated['date'], $productName));
    }

    public function brandStatisticsSalesByDay(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $productName = $this->request->input('product_name');
        return $this->response->success($this->service->brandStatisticsSalesByDay($validated['brand_code'], $validated['date'], $productName));
    }

    public function monthPlatformReport(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $productName = $this->request->input('product_name');
        return $this->response->success($this->service->monthPlatformReport($validated['brand_code'], $validated['date'], $productName));
    }

}