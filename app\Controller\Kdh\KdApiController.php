<?php

namespace App\Controller\Kdh;
use App\Controller\AbstractController;
use App\Core\Services\KuaidiHundred\KdhApiService;
use App\Core\Utils\Response;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\ControllerNameAnnotation;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("快递100接口")
 * @AutoController()
 */
class KdApiController extends AbstractController
{
    /**
     * @Inject()
     * @var KdhApiService
     */
    protected $service;

    /**
     * @Inject()
     * @var Response
     */
    protected $response;

    public function detocr()
    {
        $data = $this->request->all();
        $result = $this->service->detocr($data);
        return $this->response->success($result);
    }

    public function query()
    {
        $data = $this->request->all();
        $result = $this->service->detocr($data);
        return $this->response->success($result);
    }
}