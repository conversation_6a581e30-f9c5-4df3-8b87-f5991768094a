<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/18 下午2:18
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\MpWx;

use App\Annotation\MpWxTokenAnnotation;
use App\Core\Services\MpWx\MpWxArticleService;
use App\Core\Services\MpWx\MpWxBaseService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class ArticleController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MpWxBaseService
     */
    protected $mpWxBaseService;

    /**
     * @Inject()
     * @var MpWxArticleService
     */
    protected $mpWxArticleService;

    public function getArticleSummary()
    {
        $result = $this->mpWxArticleService->getArticleSummary();
        return $this->response->success($result);
    }

    public function getArticleTotal()
    {
        $result = $this->mpWxArticleService->getArticleTotal();
        return $this->response->success($result);
    }

    public function getUserRead()
    {
        $result = $this->mpWxArticleService->getUserRead();
        return $this->response->success($result);
    }


}