<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/17 下午4:38
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing\TongjiBd;

use App\Core\Services\TongjiBd\TongjiBdReportService;
use App\Core\Services\Marketing\SiteReportService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use App\Request\TongjiBd\ReportRequest;
use Psr\Http\Message\ResponseInterface;

/**
 * @AutoController()
 */
class ReportController extends \App\Controller\BaseController
{
    /**
     * @Inject
     * @var TongjiBdReportService
     */
    protected $tongjiBdReportService;

    /**
     * @Inject()
     * @var SiteReportService
     */
    protected $siteReportService;


    function index()
    {
        return 1;
    }

    /**
     * 站点数据慨怳
     * @return ResponseInterface
     */
    public function siteTrendRpt(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->siteReportService->siteTrendRpt($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * 站点流量趋势分析
     * @return ResponseInterface
     */
    public function siteTrendRptByDate(): ResponseInterface
    {
        $dayType    = $this->request->input('day_type', 'day');
        if($dayType){
            $param['day_type'] = $dayType;
        }
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result     = $this->siteReportService->siteTrendRptByDate($filter, $op, $sort, $order, $limit, $param);
        return $this->response->success($result);
    }

    /**
     * 站点来源页面访问数据列表
     * @return ResponseInterface
     */
    public function sourcePageList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->siteReportService->sourcePageList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * 受访页面列表
     * @return ResponseInterface
     */
    public function visitPageList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->siteReportService->visitPageList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * 访问站点地区分布数据
     * @return ResponseInterface
     */
    public function visitAreaRptList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->siteReportService->visitAreaRpt($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function test()
    {
        return 1;
    }
}