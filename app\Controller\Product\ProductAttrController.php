<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Product\ProductAttrService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("产品属性管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductAttrController extends BaseController
{
    /**
     * @var ProductAttrService
     * @Inject()
     */
    protected $service;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $sort = $this->request->input('sort', 'sort');
        $order = $this->request->input('order', 'DESC');
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit);
        return $this->response->success($result);
    }
}