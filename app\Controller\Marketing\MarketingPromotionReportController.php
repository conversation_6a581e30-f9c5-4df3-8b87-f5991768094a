<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing;

use App\Core\Services\Marketing\MarketingPromotionReportService;
use App\Request\Marketing\BrandCodeDateRequest;
use App\Request\Marketing\MarketingPromotionReport\DoEditFilterRequest;
use App\Request\Marketing\MarketingPromotionReport\getAssignSummayRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class MarketingPromotionReportController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MarketingPromotionReportService
     */
    protected $service;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * 品牌推广数据汇总
     * @param BrandCodeDateRequest $request
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function brandStatistics(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->brandStatistics($validated['brand_code'], $validated['date']));
    }

    public function getAssignSummay(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->getAssignSummay($validated['brand_code'], $validated['date']));
    }

    public function monthPlatformPromotion(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        $platformId = $this->request->input('platform_id');
        return $this->response->success($this->service->monthPlatformPromotion($validated['brand_code'], $validated['date'], $platformId));
    }

    public function getBrandPromtion(BrandCodeDateRequest $request)
    {
        $validated = $request->validated();
        return $this->response->success($this->service->getBrandPromtion($validated['brand_code'], $validated['date']));
    }

    public function doEditFilter(DoEditFilterRequest $request)
    {
        $validated = $request->validated();
        $values = $this->request->all();
        return $this->response->success($this->service->doEditFilter($validated['brand_code'], $validated['platform_id'], $validated['date'], $values));
    }

    public function saveBrandPromtion()
    {
        $items = $this->request->input('items', []);
        return $this->response->success($this->service->saveBrandPromtion($items));
    }

}