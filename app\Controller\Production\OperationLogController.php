<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Production\Log\ProductionOperationLogService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("生产日志")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OperationLogController extends BaseController
{
    /**
     * @var ProductionOperationLogService
     * @Inject()
     */
    protected $service;
}
