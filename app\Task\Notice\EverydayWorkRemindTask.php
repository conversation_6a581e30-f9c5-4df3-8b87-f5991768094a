<?php

namespace App\Task\Notice;

use App\Core\Utils\Log;
use App\Core\Services\Notice\NoticeService;
use App\Model\TchipBi\UserModel;

class EverydayWorkRemindTask
{
    /**
     * @return void
     */
    public function execute()
    {
        $taskName = '每日工作提醒任务';
        if (env('CRONTAB_ONOFF', false) === true) {
            Log::get('system', 'system')->info('==============================================================================');
            Log::get('system', 'system')->info("开始执行{$taskName}");
            make(NoticeService::class)->everydayWorkRemind();
            Log::get('system', 'system')->info("开始执行");
            Log::get('system', 'system')->info('==============================================================================');
        } else {
            Log::get('system', 'system')->info("[{$taskName}] 系统未开启任务执行功能.");
        }
    }
}