<?php
declare(strict_types=1);
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/18 下午4:40
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\Index;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Index\FastEntranceService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * 快捷入口
 * @ControllerNameAnnotation("快捷入口")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class FastEntranceController extends \App\Controller\BaseController
{

    /**
     * @Inject()
     * @var FastEntranceService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("树型列表")
     * @Middleware(MenuMiddleware::class)
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getTreeList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getTreeList($filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    public function myFastEntrance()
    {
        $result = $this->service->myFastEntrance();
        return $this->response->success($result);
    }

    public function roleFastEntranceList()
    {
        $roleId = $this->request->input('role_id');
        $result = $this->service->roleFastEntranceList((int)$roleId);
        return $this->response->success($result);
    }

    public function doEditMyFast()
    {
        $ids = $this->request->input('ids');
        if (!$ids) {
            $this->response->error(__('common.Missing_parameter'));
        }
        $result = $this->service->doEditMyFast($ids);
        return $this->response->success($result);
    }

}