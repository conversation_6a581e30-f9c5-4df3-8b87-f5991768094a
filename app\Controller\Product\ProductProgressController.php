<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Product\ProductProgressService;
use App\Core\Services\Project\AttachmentService as proAttachmentService;

use App\Core\Services\Product\ProductService;
use App\Model\Redmine\UserCommentsModel;
use App\Request\Product\ProductRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("产品跟进")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductProgressController extends BaseController
{
    /**
     * @Inject()
     * @var proAttachmentService
     */
    protected $proAttachmentService;

    /**
     * @var ProductProgressService
     * @Inject()
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("产品动态")
     * @return ResponseInterface
     */
    public function productFollowList(): ResponseInterface
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $page = $this->request->input('page');
        $result = $this->service->productFollowList($filter, $op, $sort, $order, (int) $limit, $page ?? 0);
        $userCommentsModel = make(UserCommentsModel::class);
        $postComment = $userCommentsModel::query()->where('project_type','product')->where(function ($query) use ($filter) {
            if (!empty($filter['product_id'])) {
                $query->where('project_id', $filter['product_id']);
            }
        })->orderBy('id', 'asc')->get()->toArray();
        foreach ($postComment as $item) {
            $pid = $item['pid'];
            // 在 arr1 中找到对应的 id
            foreach ($result['data'] as &$entry) {
                if ($entry['id'] === $pid) {
                    $entry['postcomment'][] = $item;
                    break; // 找到对应的 id 后跳出循环
                }
            }
        }
        return $this->response->success($result);
    }
    public function deleteAttachment()
    {
        $id = $this->request->input('id');
        // $result = $this->attachmentService->delete($id);
        $result = $this->proAttachmentService->doDelete($id);
        return $this->response->success($result);
    }

}