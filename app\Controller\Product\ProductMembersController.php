<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Product\ProductMembersService;
use App\Model\Redmine\ProductMemberModel;
use App\Model\Redmine\ProductModel;
use App\Request\Product\ProductRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("产品成员")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductMembersController extends BaseController
{
    /**
     * @var ProductMembersService
     * @Inject()
     */
    protected $service;
    
    public function memberList()
    {
        $productId = $this->request->input('product_id');
        if ($productId) {
            $result = $this->service->memberList($productId);
        }
        return $this->response->success($result ?? []);
    }

    public function joinAllProduct()
    {
        $uid = $this->request->input('uid');
        $products = ProductModel::query()->get();
        foreach ($products as $product) {
            ProductMemberModel::query()->updateOrCreate(['user_id'=>$uid, 'product_id'=>$product->id]);
        }
        return $this->response->success();
    }
}