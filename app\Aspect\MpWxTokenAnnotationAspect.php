<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/4/19 下午5:28
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Aspect;

use App\Annotation\MpWxTokenAnnotation;
use App\Annotation\WorkWxTokenAnnotation;
use App\Constants\AutoUserCountCode;
use App\Core\Services\MpWx\MpWxBaseService;
use App\Core\Services\WorkWx\WorkWxBaseService;
use Hyperf\Di\Annotation\AnnotationCollector;
use Hyperf\Di\Annotation\Aspect;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Di\Aop\ProceedingJoinPoint;
use App\Constants\StatusCode;
use App\Exception\AppException;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * AOP切面-微信公众号注解切入
 * Class FirmwareVersionAnnotationAspect
 * @package App\Aspect
 * @Aspect()
 */
class MpWxTokenAnnotationAspect extends \Hyperf\Di\Aop\AbstractAspect
{
    /**
     * @Inject()
     * @var RequestInterface
     */
    protected $request;

    public $classes = [];

    public $annotations = [
        MpWxTokenAnnotation::class,
    ];

    /**
     * @Inject()
     * @var MpWxBaseService
     */
    protected $mpWxBaseService;

    /**
     * @inheritDoc
     */
    public function process(ProceedingJoinPoint $proceedingJoinPoint)
    {
        $metadata = $proceedingJoinPoint->getAnnotationMetadata();

        throw new AppException('test');

        $mp = $metadata->method[MpWxTokenAnnotation::class]->mp ?? $metadata->class[MpWxTokenAnnotation::class]->mp;

        if(!$mpData = $this->mpList($mp)){
            throw new AppException(StatusCode::ERR_EXCEPTION, 'workwx get token type err:'.$mp);
        }

        $mp = $this->request->input('mp');

        if(!getCache($mpData['redis_key'])){
            $this->mpWxBaseService->getToken($mpData['appid'], $mpData['secret'], $mpData['redis_key']);
        }

        // 切面切入后，执行对应的方法会由此来负责
        // $proceedingJoinPoint 为连接点，通过该类的 process() 方法调用原方法并获得结果
        // 在调用前进行某些处理
        $result = $proceedingJoinPoint->process();
        // 在调用后进行某些处理
        return $result;
    }

    public function mpList($mp)
    {
        $items = [
            'stationpc' => [
                'appid' => env('MP_STATIONPC_APPID'),
                'secret' => env('MP_STATIONPC_SECRET'),
                'redis_key' => AutoUserCountCode::REDISKEY_MP_WX_STATIONPC_TOKEN
            ],
        ];

        return $items[$mp] ?? '';
    }

}