<?php

declare(strict_types=1);

namespace App\Command\TongjiBd;

use Hyperf\Di\Annotation\Inject;
use App\Core\Utils\TimeUtils;
use App\Model\TongjiSite\TongjiSiteModel;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;


/**
 * @Command
 */
class SiteReportCommand extends HyperfCommand
{
    /**
     * 执行的命令行
     *
     * @var string
     */
    protected $name = 'siteReport:command';

    /**
     * @var ContainerInterface
     */
    protected $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        $this->tongjiBdReportService = make(\App\Core\Services\TongjiBd\TongjiBdReportService::class);

        parent::__construct('tongjiBd:SiteReportCommand');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('百度站点统计');
        $this->addOption('dated', 'N', InputOption::VALUE_REQUIRED, '日期');
    }

    /**
     * @param date $dated 日期格式:2022.07.01-2022.07.19
     * @return void
     */
    public function handle()
    {
        $dated = $this->input->getOption('dated');
        if(!$dated){
            $this->line('请填写日期', 'info');
            return;
        }
        $dated = explode('-', $dated);
        $dated[0] = implode('-', explode('.', $dated[0]));
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        if($dated[0] > $yesterday){
            $this->line('开始日期不能大于今天', 'info');
            return;
        }else if (empty($dated[1]) || $dated[0] == $yesterday) {
            $dated[1] = $yesterday;
        }else{
            $dated[1] = implode('-', explode('.', $dated[1]));
            if ($dated[1] < $dated[0]) {
                $dated[1] = $dated[0];
            }else if($dated[1] > $yesterday){
                $dated[1] = $yesterday;
            }
        }
        $this->line('开始'.implode('~', $dated), 'info');
        $dateDiff = TimeUtils::getTimeNum($dated[0], $dated[1]);
        $rows = TongjiSiteModel::query()->where('status', 1)->get();
        for($i = 0; $i<=$dateDiff; $i++){
            $dayTime = strtotime("+{$i} day", strtotime($dated[0]));
            $this->line(date('Y-m-d', $dayTime).'开始', 'info');
            foreach ($rows as $row){
                // $this->tongjiBdReportService->reportDayTrend($row->site_id, $dayTime);
                // 受访，登陆页面统计
                $this->tongjiBdReportService->reportCommonTrack($row->site_id, $dayTime);
                // 来源数据-按类型-pc
                $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_type, 'pc');
                // 来源数据-按类型-mobile
                $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_type,'mobile');
                // 来源数据-按来源-pc
                $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_site, 'pc');
                // 来源数据-按来源-mobile
                $this->tongjiBdReportService->reportSource($row->site_id, $dayTime, $this->tongjiBdReportService->source_view_site,'mobile');
                // 地区数据-按省
                $this->tongjiBdReportService->visitAreaRpt($row->site_id, $dayTime);
                // 地区数据-国家
                $this->tongjiBdReportService->visitAreaRpt($row->site_id, $dayTime, 'world');
            }
        }
        $this->line('统计完成.', 'info');
    }
}
