<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\ProductionCode;
use App\Constants\ProductionOrderCode;
use App\Constants\StatusCode;
use App\Controller\BaseController;
use App\Core\Services\ProductionFactory\ProductionFactoryService;
use App\Core\Services\ProductionOrder\ProductionOrderService;
use App\Core\Services\UserService;
use App\Exception\AppException;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Exception;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("生产订单")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OrderController extends BaseController
{
    /**
     * @var ProductionOrderService
     * @Inject()
     */
    protected $service;

    public function getList()
    {
        return parent::getList();
    }

    public function overView()
    {
        return parent::overView();
    }

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function doEdit()
    {
        return parent::doEdit();
    }

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function doDelete()
    {
        return parent::doDelete();
    }

    /**
     * 获取生产订单的工作状态
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getWorkStatusList()
    {
        return $this->response->success($this->service->getWorkStatusList());
    }

    /**
     * 获取最大的MAC值
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getMaxMac()
    {
        return $this->response->success($this->service->getMaxMac());
    }

    /**
     * 生产订单配置项
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function conf()
    {
        //软硬件人部门负责人员返回，过滤掉文总，用作默认软硬件人查询
        $hard_dev_user = make(UserService::class)->getUserByDepartmentName('硬件研发中心');
        $hard_design_user = make(UserService::class)->getUserByDepartmentName('硬件设计组');
        $soft_user = make(UserService::class)->getUserByDepartmentName('系统软件研发中心');
        $data = [
            'approve_status' => associativelyIndex(ProductionOrderCode::APPROVE_STATUS),
            'attachment_audit_status' => associativelyIndex(ProductionOrderCode::ATTACHMENT_AUDIT_STATUS),
            'summary_status' => associativelyIndex(ProductionOrderCode::SUMMARY_STATUS),
            'work_status' => $this->service->getWorkStatusList(),
            'factory_list' => make(ProductionFactoryService::class)->getAllList(),
            'hardware_user_ids' =>array_values(array_diff(array_merge($hard_dev_user,$hard_design_user),[111])),
            'software_user_ids' =>array_values(array_diff($soft_user,[111])),
            'exception_status' => associativelyIndex(ProductionCode::EXCEPTION_STATUS),
        ];
        return $this->response->success($data);
    }

    /**
     * 获取状态流
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getFlowStatus()
    {
        $id = $this->request->input('id', 0);
        $result = $this->service->getWorkFlow($id);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("生产订单推送")
     * @Middleware(MenuMiddleware::class)
     */
    public function sendOrderMessage()
    {
        $value = $this->request->input('value', []);
        $result = $this->service->sendOrderMessage($value);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("同步订单")
     * @Middleware(MenuMiddleware::class)
     */
    public function syncOrder()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        if (empty($filter['checkdate'])) {
            throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
        }
        $this->service->syncStart();
        try {
            $this->service->syncProductionOrder([
                'filter' => $filter,
                'op'     => $op
            ]);
        } catch (Exception $e) {
            throw new AppException(StatusCode::VALIDATION_ERROR, $e->getMessage());
        } finally {
            $this->service->syncEnd();
        }

        return $this->response->success();
    }
}
