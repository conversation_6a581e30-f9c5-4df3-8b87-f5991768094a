#应用语言 zh_CN - 中文， en - 英文
APP_LANG=zh_CN
APP_NAME=tchip_bi
APP_ENV=dev
SCAN_CACHEABLE=false

#服务端口
SERVER_HTTP_PORT=8057
SWOOLE_CPU_NUM=2

DB_DRIVER=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=hyperf
DB_USERNAME=root
DB_PASSWORD=
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=
DB_MAX_CONNECTIONS=10

REDIS_HOST=localhost
REDIS_AUTH=(null)
REDIS_PORT=6379
REDIS_DB=0
REDIS_PREFIX=tchip:bi:

DB_TCHIP_OA_HOST=*************
DB_TCHIP_OA_DATABASE=tchip_oa_20221010
DB_TCHIP_OA_USERNAME=root
DB_TCHIP_OA_PASSWORD=123
DB_TCHIP_OA_PREFIX=tchip_
TCHIP_OA_URL = http://oa.t-chip.com.cn

#tchip_bi_bbs mysql配置
DB_TCHIP_BBS_HOST=*************
DB_TCHIP_BBS_DATABASE=tchip_bi_bbs
DB_TCHIP_BBS_USERNAME=root
DB_TCHIP_BBS_PASSWORD=123
DB_TCHIP_BBS_PREFIX=bi_bbs_
DB_TCHIP_BBS_CHARSET=utf8mb4
DB_TCHIP_BBS_COLLATION=utf8mb4_general_ci

# redmine mysql配置
DB_TCHIP_REDMINE_HOST=************
DB_TCHIP_REDMINE_DATABASE=firefly_redmine_20240414
DB_TCHIP_REDMINE_USERNAME=root
DB_TCHIP_REDMINE_PASSWORD=123
TCHIP_SALE_ERP_URL=http://***********:8080/

##定时任务开关
CRONTAB_ONOFF=true
WXWORK_PUSH_SALEORDER=false
#每周定时通知员工填写周报
INFORM_USER_REPORT=false
##检测ERP产品库存变化
ERP_PRODUCT_STOCK_CHACK=false
##同步销售系统产品状态
SALE_PRODUCT_SYNC_STATUS=false
##检测IPTVBOX文件变化
STATIONPC_IPTVBOX_CHANGE=false

##邮件配置
MAIL_MAILER=smtp
MAIL_SMTP_HOST=smtp.exmail.qq.com
MAIL_SMTP_PORT=465
MAIL_SMTP_USERNAME=<EMAIL>
MAIL_SMTP_PASSWORD=hUtBevE7m8HH6cQM
MAIL_SMTP_ENCRYPTION=ssl
MAIL_SMTP_TIMEOUT=null
MAIL_SMTP_AUTH_MODE=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=数字天启系统通知

##stationpc_manager管理后台IPTV源文件变化通知
IPTV_NOTICE_WORKID=Feng
IPTV_NOTICE_MAIL=<EMAIL>

BI_FRONTEND_HOST=http://bi.t-firefly.com:2101
BI_BACKEND_HOST=http://***************:8057

TCHIP_BI_BBS_URL=http://127.0.0.1:8046

## 论坛访问地址
FIREFLY_BBSCN_URL=http://***************:8026/
FIREFLY_BBSEN_URL=http://***************:8027/

UPLOAD_MIME_TYPE=jpg,png,bmp,jpeg,gif,zip,rar,xls,xlsx,wav,mp4,mp3,pdf,7z,img,txt,webp,ppt,pptx,stp,step,obj,pcb,sch
UPLOAD_MAX_SIZE=100mb

## 快递100APIKEY
KDH_APIKEY=thbSYrfF2472

# redmine部分
REDMINE_URL=http://**************:8300/
REDMINE_FILE_URL=http://**************:8063/files/
REDMINE_AUTO_CREATE_PROJECT_FLOW=true