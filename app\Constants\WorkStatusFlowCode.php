<?php

namespace App\Constants;

use App\Core\Services\AssembleOrder\AssembleOrderInfoService;
use App\Core\Services\ProductionOrder\ProductionOrderInfoService;
use App\Model\TchipBi\AssembleOrderInfoModel;
use App\Model\TchipBi\ProductionOrderInfoModel;

class WorkStatusFlowCode
{
    //不同场景的工作状态标识字段
    const   STATUS_RELATION_CLASS = [
        WorkFlowSceneCode::SCENE_PRODUCTION_ORDER => [
            'changeStatusClass' => [ProductionOrderInfoModel::class,'work_status_id'],//修改状态的模型类和对应字段
            'sendNoticeClass' => [ProductionOrderInfoService::class,'sendNoticeByWorkStatus'],//发送消息的服务类和方法
        ],
        WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER   => [
            'changeStatusClass' => [
                AssembleOrderInfoModel::class,
                'work_status_id'
            ],
            //修改状态的模型类和对应字段
            'sendNoticeClass'   => [
                AssembleOrderInfoService::class,
                'sendNoticeByWorkStatus'
            ],
            //发送消息的服务类和方法
        ],
    ];

    //默认的工作状态
    const DEFAULT_WORK_STATUS_ARR = [
        WorkFlowSceneCode::SCENE_PRODUCTION_ORDER => [
            [
                'name' => '资料录入',
                'key' => 'data_to_insert',
                'sort' => '1000'
            ],
            [
                'name' => '资料待审',
                'key' => 'data_to_audit',
                'sort' => '990'
            ],
            [
                'name' => '预计上线日期',
                'key' => 'predict_online_time',
                'sort' => '980'
            ],
            [
                'name' => '首件资料录入',
                'key' => 'first_to_insert',
                'sort' => '970'
            ],
//            [
//                'name' => '首件资料待审',
//                'key' => 'first_to_audit',
//                'sort' => '960'
//            ],
            [
                'name' => '生产中',
                'key' => 'producing',
                'sort' => '950'
            ],
            [
                'name' => '待核准',
                'key' => 'to_approve',
                'sort' => '940'
            ],
            [
                'name' => '完成',
                'key' => 'finished',
                'sort' => '930'
            ],
        ],
        //        WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER => [
        //            [
        //                'name' => '备料中',
        //                'key'  => 'preparing_materials',
        //                'sort' => '1000'
        //            ],
        //            [
        //                'name' => '已备未发',
        //                'key'  => 'prepared_not_sent',
        //                'sort' => '990'
        //            ],
        //            [
        //                'name' => '已转组装',
        //                'key'  => 'transferred_to_assembly',
        //                'sort' => '980'
        //            ],
        //            [
        //                'name' => '待总结',
        //                'key'  => 'pending_summary',
        //                'sort' => '970'
        //            ],
        //            [
        //                'name' => '待核准',
        //                'key'  => 'to_approve',
        //                'sort' => '940'
        //            ],
        //            [
        //                'name' => '完成',
        //                'key'  => 'finished',
        //                'sort' => '930'
        //            ],
        //        ]
        WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER => [
            [
                'name' => '订单确认',
                'key'  => 'confirm_order',
                'sort' => '1000'
            ],
            [
                'name' => '生产准备',
                'key'  => 'production_preparation',
                'sort' => '990'
            ],
            [
                'name' => '组装首件',
                'key'  => 'first_assembly',
                'sort' => '980'
            ],
            [
                'name' => '生产中',
                'key'  => 'producing',
                'sort' => '970'
            ],
            [
                'name' => '组装总结',
                'key'  => 'assemble_summary',
                'sort' => '960'
            ],
//            [
//                'name' => '发货',
//                'key'  => 'shipping',
//                'sort' => '950'
//            ],
            [
                'name' => '完成',
                'key'  => 'finished',
                'sort' => '940'
            ],
        ]
    ];

    //默认的工作流
    const FLOW_DEFAULT = [
        WorkFlowSceneCode::SCENE_PRODUCTION_ORDER => [
            //-----------------------------------正向流转【start】---------------------------------
            [
                'start' => 'data_to_insert',
                'end'   => 'data_to_audit',
                'level' => 1000,
                'formula' => 'base_file_upload_status==1',
                'description' => "基础文件上传完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start' => 'data_to_insert',
                'end'   => 'predict_online_time',
                'level' => 1100,
                'formula' => 'base_file_upload_status==1&&base_file_audit_status==1',
                'description' => "基础文件上传完成且审核完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start' => 'data_to_audit',
                'end'   => 'predict_online_time',
                'level' => 1000,
                'formula' => 'base_file_audit_status==1',
                'description' => "基础文件审核通过",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start' => 'predict_online_time',
                'end'   => 'first_to_insert',
                'level' => 1000,
                'formula' => "predict_online_time != ''",
                'description' => "上线时间确认",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start' => 'first_to_insert',
                'end'   => 'producing',
                'level' => 1000,
                'formula' => 'first_file_upload_status==1',
                'description' => "首件文件上传完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
//            [
//                'start' => 'first_to_audit',
//                'end'   => 'producing',
//                'level' => 1000,
//                'formula' => 'first_file_audit_status==1',
//                'description' => "首件文件审核通过",
//                'is_send_notice' => '1',
//            ],
            [
                'start' => 'producing',
                'end'   => 'to_approve',
                'level' => 1000,
                'formula' => 'summary_finish_status!=0',
                'description' => "生产总结完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start' => 'to_approve',
                'end'   => 'finished',
                'level' => 1000,
                'formula' => 'approve_status>1',
                'description' => "核准通过",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            //-----------------------------------正向流转【end】---------------------------------
            //-----------------------------------反向流转【start】---------------------------------
            [
                'start' => 'finished',
                'end'   => 'to_approve',
                'level' => 1000,
                'formula' => 'approve_status==1',
                'description' => "核准状态为待核准",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start' => 'to_approve',
                'end'   => 'producing',
                'level' => 1000,
                'formula' => 'summary_finish_status==0',
                'description' => "生产总结状态为新建",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start' => 'producing',
                'end'   => 'first_to_insert',
                'level' => 1000,
                'formula' => 'first_file_upload_status==0',
                'description' => "首件文件上传未完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
//            [
//                'start' => 'first_to_audit',
//                'end'   => 'first_to_insert',
//                'level' => 1000,
//                'formula' => 'first_file_audit_status==2||first_file_audit_status==0',
//                'description' => "首件文件审核不通过",
//                'is_send_notice' => '1',
//            ],
            [
                'start' => 'first_to_insert',
                'end'   => 'predict_online_time',
                'level' => 1000,
                'formula' => "predict_online_time == ''",
                'description' => "预计上线时间为空",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start' => 'predict_online_time',
                'end'   => 'data_to_audit',
                'level' => 1000,
                'formula' => 'base_file_audit_status==0',
                'description' => "基础文件审核状态为待审核",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start' => 'data_to_audit',
                'end'   => 'data_to_insert',
                'level' => 1000,
                'formula' => 'base_file_audit_status==2||base_file_audit_status==0',
                'description' => "基础文件审核不通过",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            //-----------------------------------反向流转【end】---------------------------------
        ],
        //        WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER   => [
        //            //-----------------------------------正向流转--------------------------------------
        //            [
        //                'start'          => 'preparing_materials',
        //                'end'            => 'prepared_not_sent',
        //                'level'          => 1000,
        //                'formula'        => 'material_status==2',
        //                'description'    => "齐料状态完成",
        //                'is_send_notice' => '1',
        //            ],
        //
        //            [
        //                'start'          => 'pending_summary',
        //                'end'            => 'to_approve',
        //                'level'          => 1000,
        //                'formula'        => 'summary_finish_status==2',
        //                'description'    => "总结已完成",
        //                'is_send_notice' => '1',
        //            ],
        //            [
        //                'start'          => 'to_approve',
        //                'end'            => 'finished',
        //                'level'          => 1000,
        //                'formula'        => 'approve_status>1',
        //                'description'    => "核准完成",
        //                'is_send_notice' => '1',
        //            ],
        //            //------------------------------------逆向------------------------
        //            [
        //                'start'          => 'prepared_not_sent',
        //                'end'            => 'preparing_materials',
        //                'level'          => 1000,
        //                'formula'        => 'material_status!=2',
        //                'description'    => "齐料状态未完成",
        //                'is_send_notice' => '1',
        //            ],
        //            [
        //                'start'          => 'to_approve',
        //                'end'            => 'pending_summary',
        //                'level'          => 1000,
        //                'formula'        => 'summary_finish_status!=2',
        //                'description'    => "总结未完成",
        //                'is_send_notice' => '1',
        //            ],
        //            [
        //                'start'          => 'finished',
        //                'end'            => 'to_approve',
        //                'level'          => 1000,
        //                'formula'        => 'approve_status==1',
        //                'description'    => "核准未完成",
        //                'is_send_notice' => '1',
        //            ],
        //        ]
        WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER   => [
            //-----------------------------------正向流转--------------------------------------
            //订单确认正向流转
            [
                'start'         => 'confirm_order',
                'end'           => 'finished',
                'level'          => 2000,
//                'formula'       => 'is_complete!=0&&(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1&&ship_data_status==2',
                'formula'       => 'is_complete!=0&&(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
                'description'   => "订单确认->完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
//            [
//                'start'         => 'confirm_order',
//                'end'           => 'shipping',
//                'level'          => 1950,
//                'formula'       => 'is_complete!=0&&(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
//                'description'   => "订单确认->发货",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 0,
//            ],
            [
                'start'         => 'confirm_order',
                'end'           => 'assemble_summary',
                'level'          => 1900,
                'formula'       => 'is_complete!=0&&(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) ',
                'description'   => "订单确认->组装总结",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start'         => 'confirm_order',
                'end'           => 'producing',
                'level'          => 1850,
                'formula'       => 'is_complete!=0&&(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)',
                'description'   => "订单确认->生产中",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start'         => 'confirm_order',
                'end'           => 'first_assembly',
                'level'          => 1800,
                'formula'       => 'is_complete!=0&&(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)',
                'description'   => "订单确认->组装首件",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start'         => 'confirm_order',
                'end'           => 'production_preparation',
                'level'          => 1700,
                'formula'       => 'is_complete!=0',
                'description'   => "订单确认->生产准备",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            //生产准备正向流转
            [
                'start'         => 'production_preparation',
                'end'           => 'finished',
                'level'          => 2000,
//                'formula'       => '(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1&&ship_data_status==2',
                'formula'       => '(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
                'description'   => "生产准备->完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
//            [
//                'start'         => 'production_preparation',
//                'end'           => 'shipping',
//                'level'          => 1950,
//                'formula'       => '(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
//                'description'   => "生产准备->发货",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 0,
//            ],
            [
                'start'         => 'production_preparation',
                'end'           => 'assemble_summary',
                'level'          => 1900,
                'formula'       => '(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) ',
                'description'   => "生产准备->组装总结",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start'         => 'production_preparation',
                'end'           => 'producing',
                'level'          => 1850,
                'formula'       => '(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)&&(first_assemble_data_status==3&&first_soft_data_status==3)',
                'description'   => "生产准备->生产中",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start'         => 'production_preparation',
                'end'           => 'first_assembly',
                'level'          => 1800,
                'formula'       => '(completeness_status==4&&assemble_data_status==2&&soft_data_status==3)',
                'description'   => "生产准备->组装首件",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            //组装首件正向流转
            [
                'start'         => 'first_assembly',
                'end'           => 'finished',
                'level'          => 2000,
//                'formula'       => '(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1&&ship_data_status==2',
                'formula'       => '(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
                'description'   => "组装首件->完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
//            [
//                'start'         => 'first_assembly',
//                'end'           => 'shipping',
//                'level'          => 1950,
//                'formula'       => '(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
//                'description'   => "组装首件->发货",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 0,
//            ],
            [
                'start'         => 'first_assembly',
                'end'           => 'assemble_summary',
                'level'          => 1900,
                'formula'       => '(first_assemble_data_status==3&&first_soft_data_status==3)&&(product_finished_data_status ==3&&product_soft_data_status==3) ',
                'description'   => "组装首件->组装总结",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            [
                'start'         => 'first_assembly',
                'end'           => 'producing',
                'level'          => 1850,
                'formula'       => '(first_assemble_data_status==3&&first_soft_data_status==3)',
                'description'   => "组装首件->组装总结",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            //生产中正向流转
            [
                'start'         => 'producing',
                'end'           => 'finished',
                'level'          => 2000,
//                'formula'       => '(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1&&ship_data_status==2',
                'formula'       => '(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
                'description'   => "生产中->完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
//            [
//                'start'         => 'producing',
//                'end'           => 'shipping',
//                'level'          => 1900,
//                'formula'       => '(product_finished_data_status ==3&&product_soft_data_status==3) &&summary_finish_status!=1',
//                'description'   => "生产中->发货",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 0,
//            ],
            [
                'start'         => 'producing',
                'end'           => 'assemble_summary',
                'level'          => 1800,
                'formula'       => 'product_finished_data_status ==3&&product_soft_data_status==3',
                'description'   => "生产中->组装总结",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
            //组装总结正向流转
            [
                'start'         => 'assemble_summary',
                'end'           => 'finished',
                'level'          => 2000,
//                'formula'       => 'summary_finish_status!=1&&ship_data_status==2',
                'formula'       => 'summary_finish_status!=1',
                'description'   => "组装总结->完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 0,
            ],
//            [
//                'start'         => 'assemble_summary',
//                'end'           => 'shipping',
//                'level'          => 1900,
//                'formula'       => 'summary_finish_status!=1',
//                'description'   => "组装总结->发货",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 0,
//            ],
            //发货正向流转
//            [
//                'start'         => 'shipping',
//                'end'           => 'finished',
//                'level'          => 2000,
//                'formula'       => 'ship_data_status==2',
//                'description'   => "组装总结->完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 0,
//            ],

            //------------------------------------逆向------------------------
//            //完成逆向流转
//            [
//                'start'          => 'finished',
//                'end'            => 'confirm_order',
//                'level'          => 1000,
//                'formula'        => 'is_complete==0',
//                'description'    => "完成->订单确认：订单未完善",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'finished',
//                'end'            => 'production_preparation',
//                'level'          => 900,
//                'formula'        => 'completeness_status!=4||assemble_data_status!=2||soft_data_status!=3',
//                'description'    => "完成->生产准备：备料未转组装或组装资料未完成或软件资料未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'finished',
//                'end'            => 'first_assembly',
//                'level'          => 800,
//                'formula'        => 'first_assemble_data_status!=3||first_soft_data_status!=3',
//                'description'    => "完成->组装首件：首件组装未完成或首件软件未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'finished',
//                'end'            => 'producing',
//                'level'          => 750,
//                'formula'        => 'product_data_status!=3',
//                'description'    => "完成->生产中：生产资料未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'finished',
//                'end'           => 'assemble_summary',
//                'level'         => 700,
//                'formula'       => 'summary_finish_status==1',
//                'description'   => "完成->组装总结：总结未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'finished',
//                'end'           => 'shipping',
//                'level'         => 650,
//                'formula'       => 'ship_data_status!=2',
//                'description'   => "完成->发货：发货资料未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            //发货中逆向流转
//            [
//                'start'          => 'shipping',
//                'end'            => 'confirm_order',
//                'level'          => 1000,
//                'formula'        => 'is_complete==0',
//                'description'    => "发货->订单确认：订单未完善",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'shipping',
//                'end'            => 'production_preparation',
//                'level'          => 900,
//                'formula'        => 'completeness_status!=4||assemble_data_status!=2||soft_data_status!=3',
//                'description'    => "发货->生产准备：备料未转组装或组装资料未完成或软件资料未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'shipping',
//                'end'            => 'first_assembly',
//                'level'          => 800,
//                'formula'        => 'first_assemble_data_status!=3||first_soft_data_status!=3',
//                'description'    => "发货->组装首件：首件组装未完成或首件软件未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'shipping',
//                'end'            => 'producing',
//                'level'          => 750,
//                'formula'        => 'product_data_status!=3',
//                'description'    => "发货->生产中：生产资料未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'shipping',
//                'end'           => 'assemble_summary',
//                'level'         => 700,
//                'formula'       => 'summary_finish_status==1',
//                'description'   => "发货->组装总结：总结未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            //组装总结逆向流转
//            [
//                'start'          => 'assemble_summary',
//                'end'            => 'confirm_order',
//                'level'          => 1000,
//                'formula'        => 'is_complete==0',
//                'description'    => "组装总结->订单确认：订单未完善",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'assemble_summary',
//                'end'            => 'production_preparation',
//                'level'          => 900,
//                'formula'        => 'completeness_status!=4||assemble_data_status!=2||soft_data_status!=3',
//                'description'    => "组装总结->生产准备：备料未转组装或组装资料未完成或软件资料未完成",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'assemble_summary',
//                'end'            => 'first_assembly',
//                'level'          => 800,
//                'formula'        => 'first_assemble_data_status!=3||first_soft_data_status!=3',
//                'description'    => "组装总结->组装首件：首件组装未完成或首件软件未完成",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'assemble_summary',
//                'end'            => 'producing',
//                'level'          => 750,
//                'formula'        => 'product_data_status!=3',
//                'description'    => "组装总结->生产中：生产资料未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            //生产中逆向流转
//            [
//                'start'          => 'producing',
//                'end'            => 'confirm_order',
//                'level'          => 1000,
//                'formula'        => 'is_complete==0',
//                'description'    => "生产中->订单确认：订单未完善",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'producing',
//                'end'            => 'production_preparation',
//                'level'          => 900,
//                'formula'        => 'completeness_status!=4||assemble_data_status!=2||soft_data_status!=3',
//                'description'    => "生产中->生产准备：备料未转组装或组装资料未完成或软件资料未完成",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'producing',
//                'end'            => 'first_assembly',
//                'level'          => 800,
//                'formula'        => 'first_assemble_data_status!=3||first_soft_data_status!=3',
//                'description'    => "生产中->组装首件：首件组装未完成或首件软件未完成",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            //组装首件逆转
//            [
//                'start'          => 'first_assembly',
//                'end'            => 'confirm_order',
//                'level'          => 1000,
//                'formula'        => 'is_complete==0',
//                'description'    => "组装首件->订单确认：订单未完善",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            [
//                'start'          => 'first_assembly',
//                'end'            => 'production_preparation',
//                'level'          => 900,
//                'formula'        => 'completeness_status!=4||assemble_data_status!=2||soft_data_status!=3',
//                'description'    => "组装首件->生产准备：备料未转组装或组装资料未完成或软件资料未完成",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
//            //生产准备逆向流转
//            [
//                'start'          => 'production_preparation',
//                'end'            => 'confirm_order',
//                'level'          => 1000,
//                'formula'        => 'is_complete==0',
//                'description'    => "生产准备->订单确认：订单未完善",
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
            //通用逆转
            [
                'start'          => '*',
                'end'            => 'confirm_order',
                'level'          => 1000,
                'formula'        => 'is_complete==0',
                'description'    => "完成->订单确认：订单未完善",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start'          => '*',
                'end'            => 'production_preparation',
                'level'          => 900,
                'formula'        => 'completeness_status!=4||assemble_data_status!=2||soft_data_status!=3',
                'description'    => "完成->生产准备：备料未转组装或组装资料未完成或软件资料未完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start'          => '*',
                'end'            => 'first_assembly',
                'level'          => 800,
                'formula'        => 'first_assemble_data_status!=3||first_soft_data_status!=3',
                'description'    => "完成->组装首件：首件组装未完成或首件软件未完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start'          => '*',
                'end'            => 'producing',
                'level'          => 750,
                'formula'        => 'product_finished_data_status !=3 || product_soft_data_status!=3',
                'description'    => "完成->生产中：生产资料未完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
            [
                'start'          => '*',
                'end'           => 'assemble_summary',
                'level'         => 700,
                'formula'       => 'summary_finish_status==1',
                'description'   => "完成->组装总结：总结未完成",
                'is_send_notice' => '1',
                'pre_operation' => [],
                'is_reverse' => 1,
            ],
//            [
//                'start'          => '*',
//                'end'           => 'shipping',
//                'level'         => 650,
//                'formula'       => 'ship_data_status!=2',
//                'description'   => "完成->发货：发货资料未完成",
//                'is_send_notice' => '1',
//                'pre_operation' => [],
//                'is_reverse' => 1,
//            ],
        ]
    ];
}