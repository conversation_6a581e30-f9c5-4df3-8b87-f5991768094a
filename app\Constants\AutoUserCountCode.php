<?php


namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

class AutoUserCountCode extends AbstractConstants
{
    const AUTO_USER_COUNT = [
        'StationPCBbsCN' => [
            'code' => 'StationPCBbsCN',
            'name' => '中文论坛',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\StationPCBbsCNUserCount'
        ],
        'StationPCBbsEN' => [
            'code' => 'StationPCBbsEN',
            'name' => '英文论坛',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\StationPCBbsENUserCount'
        ],
        'StationPCMpWx' => [
            'code' => 'StationPCMpWx',
            'name' => 'StationPC公众号',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\StationPCMpWxUserCount'
        ],
        'Toutiao' => [
            'code' => 'Toutiao',
            'name' => '今日头条',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\ToutiaoUserCount'
        ],
        'Bilibili' => [
            'code' => 'Bilibili',
            'name' => '哔哩哔哩',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\BilibiliUserCount'
        ],
        'Douyin' => [
            'code' => 'Douyin',
            'name' => '抖音',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\DouyinUserCount'
        ],
        'Twitter' => [
            'code' => 'Twitter',
            'name' => 'Twitter',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\TwitterUserCount'
        ],
        'Youtube' => [
            'code' => 'Youtube',
            'name' => 'Youtube',
            'class' => 'App\Core\Services\Marketing\AutoUSerCount\YoutubeUserCount'
        ],
    ];
}