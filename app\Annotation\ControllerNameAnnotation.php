<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/4/19 上午10:00
 * <AUTHOR>
 * @Description
 */

namespace App\Annotation;

use Hyperf\Di\Annotation\AbstractAnnotation;

/**
 * 注解-定义controller名称
 * @package App\Annotation
 * @Annotation
 * @Target({"METHOD", "CLASS"})
 */
class ControllerNameAnnotation extends AbstractAnnotation
{
    /**
     * @var string
     */
    public $name;

    public function __construct($value = null)
    {
        parent::__construct($value);
        $this->bindMainProperty('name', $value);
    }

}