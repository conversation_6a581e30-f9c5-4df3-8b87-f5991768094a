<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductChangeRecordProjectTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_change_record_project')) {
            Schema::create('product_change_record_project', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->unsignedInteger('change_record_id')->default(0)->comment('产品变更记录id')->index();
                $table->unsignedInteger('project_id')->default(0)->comment('对应的是产品的项目id')->index();
                $table->unsignedInteger('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_change_record_project', function (Blueprint $table) {
            //
        });
    }
}
