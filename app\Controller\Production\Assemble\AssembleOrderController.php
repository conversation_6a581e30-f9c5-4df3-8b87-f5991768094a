<?php

namespace App\Controller\Production\Assemble;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Controller\BaseController;
use App\Core\Services\AssembleOrder\AssembleOrderInfoService;
use App\Core\Services\AssembleOrder\AssembleOrderMaterialCompletenessService;
use App\Core\Services\AssembleOrder\AssembleOrderService;
use App\Exception\AppException;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use App\Middleware\ProductionLogMiddleware;
use Exception;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\Middlewares;

/**
 * @ControllerNameAnnotation("组装订单")
 * @AutoController(prefix="/production/assemble_order")
 * @Middleware(AuthMiddleware::class)
 */
class AssembleOrderController extends BaseController
{
    /**
     * @var AssembleOrderService
     * @Inject()
     */
    protected $service;

    public function conf()
    {
        $data = $this->service->conf();
        return $this->response->success($data);
    }

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doEdit()
    {
        return parent::doEdit();
    }
    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doDelete()
    {
        return parent::doDelete();
    }
    /**
     * @ControllerNameAnnotation("修改齐料状态")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function changeMaterialStatus()
    {
        $id = (int)$this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = make(AssembleOrderMaterialCompletenessService::class)->doEdit($id, $params);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("修改工作状态")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function changeWorkStatus()
    {
        $id = (int)$this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = make(AssembleOrderInfoService::class)->changeWorkStatus($id, $params);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("导出")
     * @Middleware(AuthMiddleware::class)
     */
    public function export()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->export($filter, $op, $sort, $order);
        return $result;
    }
    /**
     * @ControllerNameAnnotation("同步订单")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function syncOrder()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        if (empty($filter['zzhcreatedt'])) {
            throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
        }
        $this->service->syncStart();
        try {
            $this->service->syncAssembleOrder([
                'filter' => $filter,
                'op'     => $op
            ]);
        } catch (Exception $e) {
            throw new AppException(StatusCode::VALIDATION_ERROR, $e->getMessage());
        } finally {
            $this->service->syncEnd();
        }

        return $this->response->success();
    }

    public function getStockOverView()
    {
        $code = $this->request->input('stock_order_code');
        $result = $this->service->getStockOverView($code);
        return $this->response->success($result);
    }

    public function getOrderErp()
    {
        $code = $this->request->input('code');
        $result = $this->service->getOrderErp($code);
        return $this->response->success($result);
    }

    /**
     * 获取mac或sn
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getOrderCode()
    {
        $params = $this->request->all();
        $result = $this->service->getOrderCode($params);
        return $this->response->success($result);

    }

    /**
     * 获取状态流
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getFlowStatus()
    {
        $id = $this->request->input('id', 0);
        $result = $this->service->getWorkFlow($id);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("关联订单")
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function relateOrder()
    {
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->relateOrder($id, $params);
        return $this->response->success($result);
    }
}