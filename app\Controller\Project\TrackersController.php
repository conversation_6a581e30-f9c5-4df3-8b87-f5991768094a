<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/4 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\TrackersService;
use App\Request\Project\ProjectsWatchersRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("关注管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class TrackersController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var TrackersService
     */
    protected $service;
}