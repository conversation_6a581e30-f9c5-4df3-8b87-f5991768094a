<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2024/03/11 10:36
     * <AUTHOR>
     * @Description
     */

    namespace App\Controller\Project;

    use \App\Core\Services\Project\WikiService;
    use App\Annotation\ControllerNameAnnotation;
    use GuzzleHttp\Psr7\Stream;
    use GuzzleHttp\Psr7\Utils;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\HttpServer\Annotation\Middleware;
    use App\Middleware\AuthMiddleware;
    use Hyperf\Di\Annotation\Inject;
    use Psr\Http\Message\ResponseInterface;
    use Hyperf\HttpServer\Contract\ResponseInterface as OtherResponseInterface;

    use Hyperf\HttpMessage\Stream\SwooleStream;


    /**
     * @ControllerNameAnnotation("Wiki")
     * @AutoController
     * @Middleware(AuthMiddleware::class)
     */
    class WikiController extends \App\Controller\BaseController
    {
        /**
         * @Inject()
         * @var WikiService
         */
        protected $service;

        public function getList()
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
//            var_dump($filter);
            $result = make(\App\Core\Services\Project\WikiService::class)->getList($filter, $op, $sort, $order, $limit);
            return $this->response->success($result);
        }

        public function doEdit()
        {
            $id = $this->request->input('id',0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEdit($id, $params);
            return $this->response->success($result);
        }

        public function doDelete()
        {
            $page_id = $this->request->input('page_id',0);
            $params = $this->request->all();
            unset($params['page_id']);
            $result = $this->service->doDeleteWikiPageAndContent($page_id);
            return $this->response->success($result);
        }

        public function checkDuplicateTitle()
        {
            $title = $this->request->input('title',0);
            $project_id = $this->request->input('project_id',0);
            $result = $this->service->checkDuplicateTitle($title, $project_id);
            return $this->response->success($result);
        }

        public function updateWikiTitle()
        {
            $page_id = $this->request->input('page_id',0);
            $title = $this->request->input('title','');
            $project_id = $this->request->input('project_id',0);
            $result = $this->service->updateWikiTitle($page_id, $title, $project_id);
            return $result ? $this->response->success('标题更新成功') : $this->response->error('标题更新失败');

        }

        public function deleteWikihistory()
        {
            $id = $this->request->input('id',0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->deleteWikihistory($id,$params);
            return $result ? $this->response->success('删除成功') : $this->response->error('删除失败');

        }

        public function updateWikiProtected()
        {
            $page_id = $this->request->input('page_id',0);
            $protected = $this->request->input('protected','');
            $result = $this->service->updateWikiProtected($page_id,$protected);
            return $result ? $this->response->success('修改成功') : $this->response->error('修改失败');

        }

        public function editWikiHistory()
        {
            $id = $this->request->input('id',0);
            $comment = $this->request->input('comment','');
            $result = $this->service->editWikiHistory($id,$comment);
            return $result ? $this->response->success('修改成功') : $this->response->error('修改失败');
        }


        public function  getWikiHistory()
        {
            $page_id = $this->request->input('page_id',0);
            $result = $this->service->getWikiHistory($page_id);
            return $this->response->success($result);
        }



        public function exportPdfOrHtml()
        {
            $page_id = $this->request->input('page_id', 0);
            $file_type = $this->request->input('file_type', 'pdf');
            $result = $this->service->exportPdfOrHtml($page_id, $file_type);
            if ($result) {
                $pdfContent = $result->getBody()->getContents();
                $response = make(OtherResponseInterface::class);

                // 创建一个流对象
                $stream = Utils::streamFor($pdfContent);
                // 返回响应对象
                return $response
                    ->withHeader('Content-Type', 'application/octet-stream')
                    ->withHeader('Content-Disposition', 'attachment')
                    ->withBody($stream);
            } else {
                // 处理未能获取到响应的情况
                return $this->response->error();
            }
        }

        public function updateAttachment()
        {
            $id = $this->request->input('id',0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->updateAttachment($id,$params);
            return $result ? $this->response->success('上传成功') : $this->response->error('上传失败');

        }
        public function deleteAttachment()
        {
            $id = $this->request->input('id',0);
            $result = $this->service->deleteAttachment($id);
            return $result ? $this->response->success('附件删除成功') : $this->response->error('附件删除失败');

        }

        /**
         * @ControllerNameAnnotation("重新排序函数")
         */
        public function rearrangement()
        {
            $dragNodeId = $this->request->input('drag_node_id');
            $targetNode = $this->request->input('target_node_id');
            $dropType = $this->request->input('drop_type'); // 'before' | 'after' | 'inner
            $result = $this->service->rearrangement($dragNodeId, $targetNode, $dropType);
            return $this->response->success($result);
        }
    }