<?php

declare(strict_types=1);

namespace App\Command;

use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\Marketing\MarketingContent;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;

/**
 * 导入StationPC运营数据
 * @Command
 */
class ReadExcel extends HyperfCommand
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var MarketingContent
     */
    protected $marketingContentModel;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        parent::__construct('readExcel:command');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('读取Excel表内容');
        $this->addUsage('--path 文件地址');
        // $this->addArgument('path', InputArgument::REQUIRED, '文件地址');
        $this->addOption('path', 'path', InputOption::VALUE_REQUIRED);
    }

    public function handle()
    {
        $path = $this->input->getOption('path');
        $workSheet = make(Xlsx::class)->load($path)->getSheet(0);
        $highestRow = $workSheet->getHighestRow();

        $this->output->progressStart($highestRow-6);
        $platform = '';
        $channel = '';
        $exposureAll = 0;
        $userCount = 0;
        // $this->output->table(['ID', '平台','渠道','用户数量','推送内容','类型', '2月曝光量', '3月曝光量', '4月曝光量', '总曝光量'],[
        // ]);

        Db::beginTransaction();
        try {
            for ($row=6; $row <= 8; $row++){

                $platform = $workSheet->getCellByColumnAndRow('2', $row)->getFormattedValue() ?: $platform;
                $platformArr = explode('（', $platform);
                $data['platform'] = $platform = trim($platformArr[0]);

                $data['channel'] = $workSheet->getCellByColumnAndRow('3', $row)->getFormattedValue() ?: $channel;
                $channel = $data['channel'];

                $data['user_count'] = (int)$workSheet->getCellByColumnAndRow('4', $row)->getFormattedValue() ?: $userCount;
                $userCount = $data['user_count'];

                $data['title'] = $workSheet->getCellByColumnAndRow('5', $row)->getFormattedValue();
                $data['link'] = $workSheet->getCellByColumnAndRow('5', $row)->getHyperlink()->getUrl();
                $data['type'] = $workSheet->getCellByColumnAndRow('6', $row)->getFormattedValue();
                $exposure2 = (int)$workSheet->getCellByColumnAndRow('7', $row)->getFormattedValue();
                $exposure3 = (int)$workSheet->getCellByColumnAndRow('8', $row)->getFormattedValue();
                $exposure4 = (int)$workSheet->getCellByColumnAndRow('9', $row)->getFormattedValue();
                // $data['exposure'] = (int)$workSheet->getCellByColumnAndRow('10', $row)->getFormattedValue() ?: $exposureAll;
                $data['exposure'] = $exposure2 + $exposure3 + $exposure4;

                if(empty($data['title'])){
                    $this->output->error('ID:'.$row.' 不存在推送内容，跳过');
                    break;
                }

                // $this->output->table([],[
                //     [$row, $platform, $channel, $userCount, substr($title, 0, 8).'...', $type, $exposure2, $exposure3, $exposure4, $exposureAll]
                // ]);
                $this->marketingContentModel::query()->updateOrCreate(['platform' => $platform, 'channel' => $channel, 'title'=>$data['title']],$data);
                $this->output->progressAdvance();
            }
            Db::commit();
            $this->output->progressFinish();
        }catch (\Throwable $e){
            Db::rollBack();
            $this->output->error($e->getMessage());
        }

        // var_dump($this->input->getOption('path'));
        // // $result = $this->ask('是否?(y/n)','y');
        // // var_dump($result);
        // $this->alert('successful');
        // $this->warn('warning');
        // $this->output->progressStart(100);
        // $this->output->progressAdvance(10);
        // $this->output->progressFinish();
        // $this->output->table([1,2], [[1,2]]);
        // $this->line('Hello Hyperf!', 'info');
    }
}
