<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/19 下午3:37
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\MpWx;

use App\Core\Services\MpWx\MpWxUserService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class UserController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MpWxUserService
     */
    protected $mpwxUserService;

    public function getUserSummary()
    {
        $result = $this->mpwxUserService->getUserSummary();
        return $result;
    }

    public function getUserCumulate()
    {
        $result = $this->mpwxUserService->getUserCumulate();
        return $result;
    }
}