<?php
declare(strict_types=1);
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/20 上午10:07
 * <AUTHOR> X
 * @Description
 */

namespace App\Constants;

class ProductCode
{
    /* 待上线状态 */
    const AWAIT_ONLINE = 1;

    /* 官网信息 */
    const OFFICIAL_DESC = [
        [
            'url'  => '',
            'text' => '',
            'name' => '官网中',
            'ext'  => [
                'color' => '#18BE6A',
            ]
        ],
        [
            'url'  => '',
            'text' => '',
            'name' => '官网英',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
    ];

    /* 销售平台信息 */
    const SALE_DESC = [
        [
            'url'  => '',
            'text' => '',
            'name' => '淘宝',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '天猫',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        [
            'url'  => '',
            'text' => '',
            'name' => '商城英',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
        [
            'url'  => '',
            'text' => '',
            'name' => '商城英2',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '速卖通',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '亚马逊',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
    ];

    /* 上线准备信息 */
    const LAUNCH_DESC = [
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '样机',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '上线表',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '文案',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '网页图',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '上架图',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '固件',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        [
            'url'  => '',
            'text' => '',
            'name' => '维基中',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
        [
            'url'  => '',
            'text' => '',
            'name' => '维基英',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
        [
            'url'  => '',
            'text' => '',
            'name' => '规格书',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '发货单',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        // [
        //     'url'  => '',
        //     'text' => '',
        //     'name' => '包装',
        //     'ext'  => [
        //         'color' => '#18BE6A'
        //     ]
        // ],
        [
            'url'  => '',
            'text' => '',
            'name' => '外发资料',
            'ext'  => [
                'color' => '#18BE6A'
            ]
        ],
    ];

//         <case value="0">硬件跟进</case>
//         <case value="1">软件跟进</case>
//         <case value="2">业务跟进</case>
//         <case value="3">采购跟进</case>
//         <case value="4">综合跟进</case>
//         <case value="5">结构跟进</case>
    const PROGRESS_TYPE = [
        'soft_progress' => [
            'text' => '软件进度',
            'value' => 6,
        ],
        'hard_progress' => [
            'text' => '硬件进度',
            'value' => 7,
        ],
        'hardware' => [
            'text' => '改版记录',
            'value' => 2,
        ],
        'package' => [
            'text' => '结构包装',
            'value' => 5,
        ],
        'marketing' => [
            'text' => '营销销售',
            'value' => 4,
        ],
        'live' => [
            'text' => '上线上架',
            'value' => 8,
        ],
        'assemble' => [
            'text' => '组装跟进',
            'value' => 9,
        ],
        'progress' => [
            'text' => '其它跟进',
            'value' => 1,
        ],
        'property' => [
            'text' => '属性修改',
            'value' => 3,
        ],
    ];

    const EXT_FIELD = [
        'urgency' => [
            'value' => 'urgency',
            'text' => '紧急程度',
        ],
        'shipment_predicted' => [
            'value' => 'shipment_predicted',
            'text' => '出货预测',
        ],
        'amount' => [
            'value' => 'amount',
            'text' => '量产数量',
        ],
        'curr' => [
            'value' => 'curr',
            'text' => '币种',
        ],
        'customized_status' => [
            'value' => 'customized_status',
            'text' => '定制状态',
        ],
        'cost' => [
            'value' => 'cost',
            'text' => '费用',
        ],
        'project_industry' => [
            'value' => 'project_industry',
            'text' => '项目行业',
        ],
        'company_website' => [
            'value' => 'company_website',
            'text' => '公司网址',
        ],
        'company_background' => [
            'value' => 'company_background',
            'text' => '公司背景',
        ],
    ];

    const DESC_FIELD = ['sale_desc', 'launch_desc', 'official_desc'];

    /* desc_id 产品描述(上线)状态未完成的 */
    const PRODUCT_DESC_TODO = [0, 2, 4, 6];

    /* 产品状态 - 评估 */
    const PRODUCT_STATUS_ASSESS = 7;
    /* 产品状态 - 研发 */
    const PRODUCT_STATUS_DEV = 0;
    /* 产品状态 - 试产 */
    const PRODUCT_STATUS_TP = 1;
    /* 产品状态 - 量产 */
    const PRODUCT_STATUS_MP = 2;
    /* 产品状态 - 暂停 */
    const PRODUCT_STATUS_PAUSE = 4;
    /* 产品状态 - 完成 */
    const PRODUCT_STATUS_DONE = 5;

    const PRODUCT_DEFAULT_MODULES = ['projects_info', 'projects_attr', 'projects_desc', 'projects_customized', 'activity'];

    //-------------------------------------------------------产品变更相关属性-------------------------------------------
    //分类关键字
    const PRODUCT_CHANGE_CATEGORY = 'product_change';   //总分类
    const NOTICE_TYPE_CATEGORY = 'notice_type';
    const CHANGE_REASON_CATEGORY = 'change_reason';
    const CHANGE_IMPACT_CATEGORY = 'change_impact';
    //保密等级
    const PRIVACY_LEVEL_ARR = [
        1 => '机密',
        2 => '内部',
        3 => '公开',
    ];

    const IMPORTANCE_LEVEL_ARR = [
        1 => '非常重要',
        2 => '重要',
        3 => '一般',
    ];

    //发送状态
    const SEND_STATUS_SENDED = 2;
    const SEND_STATUS_ARR = [
        1 => '未发送',
        2 => '已发送',
    ];
    //----------------------------------------------------------------------------------------------------------------

}