<?php

declare(strict_types=1);

namespace App\Command;

use App\Core\Services\Project\JournalService;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\JournalsModel;
use App\Model\TchipBi\UserThirdModel;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputArgument;

/**
 * @Command
 */
class FixIssueJournal extends HyperfCommand
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var IssueModel
     */
    protected $issueModel;

    /**
     * @Inject()
     * @var JournalsModel
     */
    protected $journalModel;

    /**
     * @Inject()
     * @var UserThirdModel
     */
    protected $userThirdModel;

    /**
     * @Inject()
     * @var \App\Core\Services\Redmine\IssueService
     */
    protected $redmineIssueService;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        parent::__construct('fix:issue-journal');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('修复 redmine 项目活动无法打开问题');
        $this->addArgument('projectId', InputArgument::REQUIRED, '项目ID');
    }

    public function handle()
    {
        $projectId = $this->input->getArgument('projectId');
        $issueList = $this->issueModel::query()->where('project_id', $projectId)
            ->get();
        $this->output->progressStart(count($issueList));
        foreach ($issueList as $iItem) {
            $journalList = $this->journalModel::query()->where('journalized_id', $iItem['id'])->get();
            foreach ($journalList as $jItem) {
                if ($jItem['notes']) {
                    $this->journalModel::query()->where('id', $jItem['id'])->delete();
                    $apiKey = $this->userThirdModel::query()->where('third_user_id', $jItem['user_id'])->value('api_key');
                    $result = $this->redmineIssueService->sendRequest("issues/${jItem['journalized_id']}.json", [
                        'json' => [
                            'issue' => ['notes' => $jItem['notes']]
                        ]
                    ], 'put', [], $apiKey);
                    $this->journalModel::query()->where('journalized_id', $jItem['journalized_id'])
                        ->where('notes', $jItem['notes'])->update(['created_on' => $jItem['created_on_origin']]);
                }
            }
            $this->output->progressAdvance();
        }
        $this->output->writeln("\n");
        $this->output->writeln("修复完成");
    }
}
