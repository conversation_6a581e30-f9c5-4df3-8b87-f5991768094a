<?php

declare(strict_types=1);

namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

/**
 * @Constants
 *
 * @method static String getMessage($code)
 *
 * 自定义业务代码规范如下：
 * 200: '服务器成功返回请求数据', //常用
 * 201: '新建或修改数据成功',
 * 202: '一个请求已经进入后台排队(异步任务)',
 * 204: '删除数据成功',
 * 400: '发出信息有误',
 * 401: '用户没有权限(令牌失效、用户名、密码错误、登录过期)', //常用
 * 402: '令牌过期', //常用
 * 403: '用户得到授权，但是访问是被禁止的',
 * 404: '访问资源不存在',
 * 406: '请求格式不可得',
 * 410: '请求资源被永久删除，且不会被看到',
 * 500: '服务器发生错误', //常用
 * 502: '网关错误',
 * 503: '服务不可用，服务器暂时过载或维护',
 * 504: '网关超时',
 *
 */
class StatusCode extends AbstractConstants
{
    /**
     * @Message("success")
     */
    const SUCCESS = 200;

    /**
     * 请求数据验证失败！
     */
    const VALIDATION_ERROR = 301;

    /**
     * @Message("Internal Server Error!")
     */
    const ERR_SERVER = 500;

    /**
     * 业务逻辑异常
     * @Message("exception.err_exception")
     */
    const ERR_EXCEPTION = 401;

    /**
     * 用户得到授权，但是访问是被禁止的
     * @Message("exception.err_forbidden")
     */
    const ERR_FORBIDDEN = 403;


}
