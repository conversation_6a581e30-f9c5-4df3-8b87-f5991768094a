<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/30 下午4:28
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\TchipOa;

use App\Constants\StatusCode;
use App\Core\Services\BaseService;
use App\Core\Services\BusinessService;
use App\Exception\AppException;
use App\Model\TchipBi\UserModel;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Guzzle\ClientFactory;

class OaBaseService extends BusinessService
{

}