<?php

namespace App\Controller\Production\Assemble;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\AssembleOrder\AssembleOrderSummaryService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\ProductionLogMiddleware;
use Hyperf\HttpServer\Annotation\Middlewares;

/**
 * @ControllerNameAnnotation("组装订单生产总结")
 * @AutoController(prefix="/production/assemble_order_summary")
 * @Middleware(AuthMiddleware::class)
 */
class AssembleOrderSummaryController extends BaseController
{
    /**
     * @Inject()
     * @var AssembleOrderSummaryService
     */
    protected $service;

    public function overView()
    {
        $id = $this->request->input('id', 0);
        $assembleOrderId = $this->request->input('assemble_order_id', 0);
        $result = $this->service->getOverView($id, $assembleOrderId);
        return $this->response->success($result);
    }

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doEdit()
    {
        return parent::doEdit();
    }
    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doDelete()
    {
        return parent::doDelete();
    }
}