<?php

namespace App\Core\Services\TchipOa;

use App\Model\TchipBi\OaQcInspectionLevel;
use App\Model\TchipBi\OaQcBatchSampleMapping;
use App\Model\TchipBi\OaQcSamplingRules;
use Hyperf\DbConnection\Db;
use InvalidArgumentException;

class OaQcSamplingService extends OaBaseService
{
    // 硬编码AQL值（根据需求：CR=0, MA=0.65, MI=1.5）
    private const AQL_VALUES = [
        'CR' => 0.0,     // 致命缺陷
        'MA' => 0.65,    // 严重缺陷
        'MI' => 1.5,     // 轻微缺陷
    ];

    // 缺陷类型中文映射
    private const DEFECT_TYPE_CHINESE = [
        'CR' => '致命缺陷',
        'MA' => '严重缺陷',
        'MI' => '轻微缺陷',
    ];

    /**
     * 根据回货数量、缺陷类型和检查水平获取抽检参数
     */
    public function getSamplingParams(int $returnQuantity, string $defectType, string $inspectionLevel = 'Normal-2'): array
    {
        return Db::transaction(function () use ($returnQuantity, $defectType, $inspectionLevel) {
            // 1. 查询检查水平
            $level = OaQcInspectionLevel::query()
                ->where('level_code', $inspectionLevel)
                ->first();

            // 2. 查询样本字码和抽样数（使用修正后的模型类）
            $mapping = OaQcBatchSampleMapping::query()
                ->where('inspection_level_id', $level->id)
                ->where('min_num', '<=', $returnQuantity)
                ->where('max_num', '>=', $returnQuantity)
                ->first();

            // 3. 根据缺陷类型获取固定AQL值
            $aqlValue = $this->getAqlByDefectType($defectType);

            // 4. 查询AC/RE值（使用修正后的模型类）
            $rule = OaQcSamplingRules::query()
                ->where('sample_code', $mapping->sample_code)
                ->where('aql_value', $aqlValue)
                ->first();

            // 5. 返回结果
            return [
                'inspection_level' => $inspectionLevel,
                'inspection_level_name' => $level->level_name,
                'standard_code' => $level->standard_code,
                'aql_value' => $aqlValue,
                'sample_code' => $mapping->sample_code,
                'sample_size' => $mapping->sample_size,
                'acceptance_num' => $rule->acceptance_num,
                'rejection_num' => $rule->rejection_num,
                'defect_type' => $defectType,
                'defect_type_chinese' => $this->getDefectTypeChinese($defectType),
                'return_quantity' => $returnQuantity,
            ];
        });
    }

    /**
     * 获取所有可用的检查水平
     */
    public function getInspectionLevels(): array
    {
        return OaQcInspectionLevel::query()
            ->select('level_code', 'level_name', 'standard_code')
            ->get()
            ->toArray();
    }

    /**
     * 根据缺陷类型获取AQL值
     */
    private function getAqlByDefectType(string $defectType): float
    {
        $upperType = strtoupper($defectType);
        
        if (!isset(self::AQL_VALUES[$upperType])) {
            throw new InvalidArgumentException("无效的缺陷类型: {$defectType}");
        }
        
        return self::AQL_VALUES[$upperType];
    }

    /**
     * 获取缺陷类型的中文名称
     */
    private function getDefectTypeChinese(string $defectType): string
    {
        $upperType = strtoupper($defectType);
        return self::DEFECT_TYPE_CHINESE[$upperType] ?? $defectType;
    }
}