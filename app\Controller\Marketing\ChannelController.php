<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:59
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing;

use App\Core\Services\Marketing\ChannelService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class ChannelController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ChannelService
     */
    protected $channelService;

    public function getList()
    {
        $result = $this->channelService->getList();
        return $this->response->success($result);
    }
}