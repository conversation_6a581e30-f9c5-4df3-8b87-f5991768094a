<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Core\Services;

use App\Core\Utils\Tree;
use App\Model\Redmine\IssueModel;
use App\Model\TchipBi\UserDepartmentBindModel;
use App\Model\TchipBi\UserDepartmentModel;
use App\Model\TchipBi\UserModel;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;

/**
 * 用户操作服务类.
 */
class MemberService extends BusinessService
{
    /**
     * @Inject
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject
     * @var UserModel
     */
    protected $userModel;

    /**
     * @Inject
     * @var UserDepartmentModel
     */
    protected $userDepartmentModel;

    /**
     * @Inject
     * @var UserDepartmentBindModel
     */
    protected $userDepartmentBindModel;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        // /* @var UserModel $query*/
        // list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        // $query->with()
    }

    /**
     * 成员结构.
     */
    public function memberStruct()
    {
        $members = UserModel::query()->with(['third' => function ($query) {
            $query->where('platform', 'redmine');
        }])->where('status', 1)->get()->toArray();
        $membersStruct = [];
        foreach ($members as $member) {
            $member['position_name'] = ! empty($member['position']) ? $member['name'] . "·{$member['position']}" : $member['name'];
            if (is_array($member['department'])) {
                foreach ($member['department'] as $dep) {
                    $membersStruct[$dep][] = $member;
                }
            } else {
                $membersStruct[$member['department']][] = $member;
            }
        }
        $departments = UserDepartmentModel::query()->whereNotIn('name', ['深圳天启', '其他（待设置部门）'])->orderBy('order', 'DESC')->get()->toArray();
        $depIds = array_column($departments, 'id');
        foreach ($departments as &$department) {
            $department['position_name'] = $department['name'];
            if (! empty($membersStruct[$department['id']])) {
                foreach ($membersStruct[$department['id']] as $key => $member) {
                    $membersStruct[$department['id']][$key]['parentid'] = $department['id'];
                    $membersStruct[$department['id']][$key]['order'] = is_array($member['order']) && ! empty($member['order'][0]) ? $member['order'][0] : 10;
                }
            }
        }
        foreach ($membersStruct as $depId => $member) {
            // 过滤没有查询到的部门成员
            if (!in_array($depId, $depIds)) continue;
            $departments = array_merge($departments, $member);
        }
        $minId = min(array_column($departments, 'parentid'));
        return make(Tree::class)->getTreeList($departments, $minId, 'parentid', 'order');
    }

    public function myTeamList()
    {
        $uid = $this->auth->user()->getId();
        $myDepartment = UserDepartmentBindModel::query()->where('user_id', $uid)->pluck('department_id')->toArray();
        $myTeam = UserModel::query()->with(['third' => function ($query) {
            $query->where('platform', 'redmine');
        }])->whereHas('userDepartmentBind', function ($query) use ($myDepartment) {
            $query->whereIn('department_id', $myDepartment);
        })->where('user.status', 1)->get()->toArray();
        $issueModel = make(IssueModel::class);

        foreach ($myTeam as &$team) {
            if (! empty($team['third']['third_user_id'])) {
                $teamIssue = $issueModel::query()->where('assigned_to_id', $team['third']['third_user_id'])->get();
                $team['issue_count'] = count($teamIssue);
                $team['issue_done'] = $teamIssue->whereIn('status_id', $issueModel->status_close)->count();
                $team['issue_todo'] = $teamIssue->whereIn('status_id', $issueModel->status_todo)->count();
            }
        }
        return $myTeam;
    }
}
