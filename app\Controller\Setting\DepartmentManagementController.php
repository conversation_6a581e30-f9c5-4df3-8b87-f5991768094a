<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午3:28
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Setting\DepartmentManagermentService;
use App\Core\Utils\Tree;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("部门管理")
 * @AutoController(prefix="/departmentManagement")
 * @Middleware(AuthMiddleware::class)
 */
class DepartmentManagementController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var DepartmentManagermentService
     */
    protected $service;

    public function userDepartment()
    {
        return $this->response->success($this->service->userDepartment());
    }

    public function departmentsUsers()
    {
        $departmentIds = $this->request->input('department_id');
        return $this->response->success($this->service->departmentsUsers($departmentIds));
    }
}