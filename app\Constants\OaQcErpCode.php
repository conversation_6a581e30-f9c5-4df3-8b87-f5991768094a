<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/20 下午6:03
 * <AUTHOR> X
 * @Description
 */

namespace App\Constants;

class OaQcErpCode
{
    /* 完工入库 */
    const QC_DONE_ENTER = 'done_enter';

    /* 采购入库 */
    const QC_PURCHASE_ENTER = 'purchase_enter';

    /* 组装入库 */
    const QC_ASSEMBLE_ENTER = 'assemble_enter';

    /* 公司组装入库-套板套件入库 */
    const QC_COAS_ENTER = 'coas_enter';

    /*调拨入库 */
    const TRANSFER_ENTER = 'transfer_enter';

    /* 其它入库 */
    const OTHER_ENTER = 'other_enter';

    /* 检测状态 - 合格 */
    const STATUS_OK = 1;

    /* 检测状态 - 不合格 */
    const STATUS_NO = 2;

    /* 检测状态 - 挂起 */
    const STATUS_PEND = 4;

    const STATUS_LIST = [
        [
            'value' => 0,
            'label' => '待检',
        ],
        [
            'value' => 1,
            'label' => '合格',
        ],
        [
            'value' => 2,
            'label' => '不合格',
        ],
        // [
        //     'value' => 3,
        //     'label' => '检测中',
        // ],
        [
            'value' => 4,
            'label' => '挂起',
        ]
    ];

    const HANLDE_LIST = [
        [
            'value' => 1,
            'label' => '特采',
        ],
        [
            'value' => 2,
            'label' => '挑选',
        ],
        [
            'value' => 3,
            'label' => '让步接收',
        ],
        [
            'value' => 4,
            'label' => '返修',
        ],
        [
            'value' => 5,
            'label' => '退货',
        ],
    ];
}