<?php

declare(strict_types=1);

namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;
use ReflectionClass;

class eConstants extends AbstractConstants
{
    private static $instance=[];
    private static function instance($class=NULL)
    {
        if ($class) {
            $key=get_class($class);
        } else {
            $key=get_called_class();
        }
        if(!isset(self::$instance[$key] )){
            self::$instance[$key] = new ReflectionClass($key);
        }
        return self::$instance[$key];
    }

    /**
     * 获取对象的属性名，已有的属性值
     * @param $class
     * @return array
     * @throws \ReflectionException
     */
    public static function get_class_constants($class = NULL)
    {
        //$reflect = new ReflectionClass(get_class( new \app\enum\eImgType() ));
        return self::instance()->getConstants();
    }

}
