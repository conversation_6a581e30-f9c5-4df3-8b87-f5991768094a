<?php

namespace App\Core\Services\Project;

use App\Constants\DataBaseCode;
use App\Model\Redmine\EnabledModuleModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class EnabledModulesService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var EnabledModuleModel
     */
    protected $model;

    public $modulesList = ['issue_tracking', 'time_tracking', 'news', 'documents', 'files', 'wiki', 'repository', 'boards', 'calendar', 'gantt'];

    public function createDefault($projectId)
    {
        return Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use($projectId){
            $defaultModules = $this->modulesList;
            $result = false;
            foreach ($defaultModules as $module) {
                $save = [
                    'project_id' => $projectId,
                    'name' => $module,
                ];
                $result = $this->model::firstOrCreate($save, $save);
            }
            return $result;
        });
    }

    public function getDefaultModules($projectId)
    {
        $data = [];
        foreach ($this->modulesList as $module) {
            $data[] = [
                'project_id' => $projectId,
                'name' => $module,
            ];
        }
        return $data;
    }

    /**
     * 新建删除项目模块
     * @param $projectId
     * @param array $values
     * @return mixed
     * @throws \Throwable
     */
    public function doEditModules($projectId, array $values)
    {
        return Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use($projectId, $values) {
            if ($values) {
                $this->model::query()->where('project_id', $projectId)->whereNotIn('name', $values)->delete();
                foreach ($values as $value) {
                    $save = [
                        'project_id' => $projectId,
                        'name' => $value,
                    ];
                    $this->model::firstOrCreate($save, $save);
                }
            } else {
                $this->model::query()->where('project_id', $projectId)->delete();
            }
            return true;
        });
    }
}