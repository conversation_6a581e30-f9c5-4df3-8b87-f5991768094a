# 批量复制事项 API 文档

## 接口信息

- **接口路径**: `/project/issue/index/batchCopyIssue`
- **请求方法**: `POST`
- **接口描述**: 批量复制事项到指定项目

## 请求参数

### 请求体 (JSON)

```json
{
  "target_project_id": 141,
  "copy_attachments": true,
  "link_to_original": true,
  "copy_description": false,
  "copy_children": false,
  "enable_sync": false,
  "issues": [
    {
      "id": 22309,
      "subject": "test使用任务的模板1",
      "tracker_id": 12,
      "status_id": 1,
      "priority_id": 3,
      "category_id": null,
      "fixed_version_id": 227,
      "parent_id": null,
      "assigned_to_id": 145,
      "assigned_to_ids": [145],
      "start_date": "2025-06-18",
      "due_date": null,
      "class_id": null,
      "watchers": [],
      "custom_fields": []
    }
  ]
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| target_project_id | int | 是 | 目标项目ID |
| copy_attachments | bool | 否 | 是否复制附件，默认true |
| link_to_original | bool | 否 | 是否与原事项建立关联关系，默认true |
| copy_description | bool | 否 | 是否复制描述，默认false |
| copy_children | bool | 否 | 是否复制子事项，默认false |
| enable_sync | bool | 否 | 是否启用同步，默认false |
| issues | array | 是 | 要复制的事项列表 |

### issues 数组中每个事项的参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 原事项ID |
| subject | string | 是 | 事项标题 |
| tracker_id | int | 是 | 跟踪器ID |
| status_id | int | 是 | 状态ID |
| priority_id | int | 是 | 优先级ID |
| category_id | int | 否 | 分类ID |
| fixed_version_id | int | 否 | 版本ID |
| parent_id | int | 否 | 父事项ID |
| assigned_to_id | int | 否 | 主要处理人ID |
| assigned_to_ids | array | 否 | 多人指派ID数组 |
| start_date | string | 否 | 开始日期 (YYYY-MM-DD) |
| due_date | string | 否 | 截止日期 (YYYY-MM-DD) |
| class_id | int | 否 | 所属分类ID |
| watchers | array | 否 | 关注人ID数组 |
| custom_fields | array | 否 | 自定义字段数组 |

## 响应结果

### 成功响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "message": "批量复制成功",
    "copied_count": 3,
    "copied_issues": [
      {
        "id": 22310,
        "subject": "test使用任务的模板1"
      },
      {
        "id": 22311,
        "subject": "测试迁移2"
      },
      {
        "id": 22312,
        "subject": "test checklist 2"
      }
    ]
  }
}
```

### 错误响应

```json
{
  "code": 500,
  "message": "批量复制失败：具体错误信息",
  "data": null
}
```

## 功能特性

1. **批量复制**: 支持一次复制多个事项
2. **附件复制**: 可选择是否复制原事项的附件
3. **关联关系**: 可选择是否在原事项和复制事项之间建立关联关系
4. **自定义字段**: 支持复制自定义字段值
5. **多人指派**: 支持复制多人指派关系
6. **关注人**: 支持复制关注人列表
7. **父子关系**: 支持复制事项的父子关系
8. **事务处理**: 使用数据库事务确保数据一致性

## 注意事项

1. 复制的事项会创建为新的事项，拥有新的ID
2. 复制后的事项作者为当前操作用户
3. 如果启用了关联关系，会在原事项和复制事项之间建立"relates"类型的关联
4. 父子关系的复制需要确保父事项也在复制列表中
5. 附件复制只复制附件记录，不复制实际文件内容
6. 操作失败时会回滚所有已执行的操作

## 使用示例

```javascript
// 前端调用示例
const copyIssues = async () => {
  const params = {
    target_project_id: 141,
    copy_attachments: true,
    link_to_original: true,
    copy_description: false,
    copy_children: false,
    enable_sync: false,
    issues: [
      {
        id: 22309,
        subject: "复制的事项标题",
        tracker_id: 12,
        status_id: 1,
        priority_id: 3,
        // ... 其他字段
      }
    ]
  };
  
  try {
    const response = await fetch('/project/issue/index/batchCopyIssue', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    });
    
    const result = await response.json();
    console.log('复制结果:', result);
  } catch (error) {
    console.error('复制失败:', error);
  }
};
```
