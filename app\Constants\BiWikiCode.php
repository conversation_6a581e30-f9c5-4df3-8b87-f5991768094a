<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2025/04/10 09:22
     * <AUTHOR>
     * @Description
     */

    namespace App\Constants;

    class BiWikiCode extends \Hyperf\Constants\AbstractConstants
    {
        // 成员角色: owner，editor，member，visitor
        // 英文名映射中文名
        public const GET_ROLE_CN = [
            'owner' => '所有者',
            'editor' => '管理员',
            'member' => '成员',
            'visitor' => '访客' ,
        ];

        // 中文名映射英文名
        public const GET_ROLE_EN = [
            '所有者' => 'owner',
            '管理员' => 'editor',
            '成员' => 'member',
            '访客' => 'visitor',
        ];

        // 定义权限等级（数值越大权限越高）
        public const ROLE_PRIORITY_EN = [
            'owner' => 4,
            'editor' => 3,
            'member' => 2,
            'visitor' => 1,
        ];

        // 定义权限等级（数值越大权限越高）
        public const ROLE_PRIORITY_CN = [
            '所有者' => 4,
            '管理员' => 3,
            '成员' =>2,
            '访客' => 1,
        ];
    }