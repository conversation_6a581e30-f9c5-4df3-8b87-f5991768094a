<?php

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Product\ProductChangeMailService;
use App\Core\Services\Product\ProductChangeRecordService;
use App\Core\Services\Product\ProductService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("产品变更记录")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductChangeRecordController extends BaseController
{
    /**
     * @Inject()
     * @var ProductChangeRecordService
     */
    protected $service;

    public function conf()
    {
        $data = $this->service->conf();
        return $this->response->success($data);
    }
    /**
     * @ControllerNameAnnotation("发送邮件通知")
     * @Middleware(MenuMiddleware::class)
     */
    public function sendMail()
    {
        $params = $this->request->all();
        $result = make(ProductChangeMailService::class)->sendMail($params);
        return $this->response->success($result);
    }

    /**
     * 获取邮件发送历史
     */
    public function getMailHistory()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = make(ProductChangeMailService::class)->getMailHistory($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * 获取BI产品列表
     */
    public function getProductOption()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = make(ProductService::class)->getProductOption($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
        return $this->response->success($result);
    }

    /**
     * 获取ERP产品列表
     */
    public function getGoodsByRedmineNames()
    {
        $names = $this->request->input('names');
        if(empty($names)){
            $result = [];
        }else{
            $result = $this->service->getGoodsByRedmineNames($names);
        }
        return $this->response->success($result);
    }

    /**
     * 获取Sale客户列表
     */
    public function getSaleClient()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getSaleClient($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
        return $this->response->success($result);
    }
}
