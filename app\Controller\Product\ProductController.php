<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Product;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Product\ProductService;
use App\Request\Product\ProductRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("产品管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductController extends BaseController
{
    /**
     * @var ProductService
     * @Inject()
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }

    public function overView()
    {
        return parent::overView(); // TODO: Change the autogenerated stub
    }

    public function productIssueJournals(ProductRequest $request)
    {
        $validated = $request->validated();
        $filter    = $this->request->input('filter');
        $op        = $this->request->input('op');
        $filter    = $filter ? (!is_array($filter) ? json_decode($filter, true) : $filter) : [];
        $op        = $op ? (!is_array($op) ? json_decode($op, true) : $op) : [];
        $result    = $this->service->productIssueJournals((int) $validated['product_id'], $filter, $op);
        return $this->response->success($result);
    }

    public function memberList(ProductRequest $request)
    {
        $validated = $request->validated();
        $result = $this->service->memberList((int) $validated['product_id']);
        return $this->response->success($result);
    }

    public function watchersBiList(ProductRequest $request)
    {
        $validated = $request->validated();
        $result = $this->service->watchersBiList((int) $validated['product_id']);
        return $this->response->success($result);
    }

    public function getStatusList()
    {
        return $this->response->success($this->service->getStatusList());
    }

    /**
     * 产品添加项目
     * @param ProductRequest $request
     * @return ResponseInterface
     */
    public function addProject(ProductRequest $request)
    {
        $validated = $request->validated();
        $projectIds = $this->request->input('project_id');
        $result = $this->service->addProject((int) $validated['product_id'], $projectIds);
        return $this->response->success($result);
    }

    /**
     * 获取产品上线跟进表配置字段
     * @return ResponseInterface
     */
    public function getFollowCode()
    {
        $code = $this->request->input('code','official_desc');
        $result = $this->service->getFollowCode($code);
        return $this->response->success($result);
    }

    public function getProgressTypeList()
    {
        $in = $this->request->input('in',[]);
        $notin = $this->request->input('notin',[]);
        $result = $this->service->getProgressTypeList($in, $notin);
        return $this->response->success($result);
    }

    public function checkFlowTaskIsFinished()
    {
        $ids = $this->request->input('ids');
        $result = $this->service->checkFlowTaskIsFinished($ids);
        return $this->response->success($result);
    }

    public function checkFlowChildTaskIsFinished()
    {
        $ids = $this->request->input('ids');
        $result = $this->service->checkFlowChildTaskIsFinished($ids);
        return $this->response->success($result);
    }

}