<?php

declare(strict_types=1);

namespace App\Command\Marketing;

use App\Core\Utils\Log;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Crontab\Event\FailToExecute;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;

/**
 * 采集用户数
 * @Command
 */
class ChannelUserCountCommand extends HyperfCommand
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        parent::__construct('channelUserCount:command');

    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('采集频道用户数');
        $this->setHelp('php bin/hyperf.php channelUserCount:command --channelId=1');
        $this->addOption('channelId', '', InputOption::VALUE_OPTIONAL, '频道ID');
    }

    public function handle()
    {
        // $this->line('Hello Hyperf!', 'info');
        $cId = $this->input->getOption('channelId');
        // $this->line($cId, 'info');
        Log::get()->info($cId);
        // FailToExecute::class;
        // $items = $this->model::query()->where('is_auto_user_count', 1)->get();
        // foreach ($items as $key => $item) {
        //     Log::get()->info('采集总数：' . count($items) . '，第' . ($key + 1).'个 ' . $item['name']);
        //     $this->collectUserCountFunc($item['id']);
        // }

    }
}
