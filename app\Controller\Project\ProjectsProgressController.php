<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Project\ProjectsProgressService;
use App\Model\Redmine\ProjectsProgressDetailsModel;
use App\Model\Redmine\UserCommentsModel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;
use Qbhy\HyperfAuth\AuthManager;

/**
 * @ControllerNameAnnotation("项目跟进")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProjectsProgressController extends BaseController
{

    /**
     * @Inject
     * @var AuthManager
     */
    protected $auth;
    /**
     * @var ProjectsProgressService
     * @Inject()
     */
    protected $service;
    public function deletePostComment()
    {
        $id = $this->request->input('id');
        $userCommentsModel = new UserCommentsModel();
        $result = $userCommentsModel::query()->where('id', $id)->delete();
        return $this->response->success($result);
    }
    public function editPostComment()
    {
        $params = $this->request->all();
        $model=UserCommentsModel::where("id",$params["id"])->where("authorid",getRedmineUserId())->first();
        if($model){
            $result=$model->update($params);
        }
        return $this->response->success($result);
    }
}