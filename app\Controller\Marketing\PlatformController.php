<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Marketing;

use App\Core\Services\Marketing\ChannelService;
use App\Core\Services\Marketing\PlatformService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class PlatformController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var PlatformService
     */
    protected $platformService;

    public function getList()
    {
        $result = $this->platformService->getList();
        return $this->response->success($result);
    }
}