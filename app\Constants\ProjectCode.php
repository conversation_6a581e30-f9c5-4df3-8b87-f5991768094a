<?php
declare(strict_types=1);

namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

class ProjectCode extends AbstractConstants
{
    /* 任务类型 */
    const PROJECT_TYPE_TASK = 'task_type';

    /* 软件类型 */
    const PROJECT_TYPE_SOFT = 'soft_type';

    /* 硬件类型 */
    const PROJECT_TYPE_HARD = 'hard_type';

    /* 产品类型 */
    const PROJECT_TYPE_PROD = 'product_type';

    /* 通用类型 */
    const PROJECT_TYPE_COM = 'common_type';

    const PROJECT_FLOW_DOEN = 1;
    const PROJECT_FLOW_DOING = 2;
    const PROJECT_FLOW_UNSTART = 3;
    const PROJECT_FLOW_TIMEOUT = 4;

    const PROGRESS_RECORD_FIEILD = ['category', 'platform', 'product_status',
        'product_type', 'hard_handler_uid', 'soft_handler_uid', 'creator', 'img',
        ];

    /* 工作跟进可见性-仅通知人可见 */
    const PROGREES_VISIBLE_NOTICE = 2;

    /* 工作跟进可见性-公开 */
    const PROGREES_VISIBLE_PUBLIC = 1;

    /* 项目角色ID - 管理员 */
    const PROJECT_ROLE_MANAGER = 4;

}