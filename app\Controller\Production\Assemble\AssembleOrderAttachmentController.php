<?php

namespace App\Controller\Production\Assemble;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\AssembleOrder\AssembleOrderAttachmentService;
use App\Middleware\AuthMiddleware;
use App\Middleware\ProductionLogMiddleware;
use App\Request\IdsRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\MenuMiddleware;
use Hyperf\HttpServer\Annotation\Middlewares;

/**
 * @ControllerNameAnnotation("组装订单附件")
 * @AutoController(prefix="/production/assemble_order_attachment")
 * @Middleware(AuthMiddleware::class)
 */
class AssembleOrderAttachmentController extends BaseController
{
    /**
     * @Inject()
     * @var AssembleOrderAttachmentService
     */
    protected $service;

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doEdit()
    {
        return parent::doEdit();
    }

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doMulti(){
        return parent::doMulti();
    }

    public function getTree()
    {
        $orderId = (int) $this->request->input('assemble_order_id', 0);
        $attachmentType = $this->request->input('attachment_type', '');
        $result = $this->service->getTreeList($orderId, $attachmentType);
        return $this->response->success($result);
    }

    /**
     * @Middlewares({
     *     @Middleware(MenuMiddleware::class),
     *     @Middleware(ProductionLogMiddleware::class)
     * })
     */
    public function doDelete()
    {
        $validated = make(IdsRequest::class)->validated();
        $params = $this->request->all();
        $result = $this->service->doDelete($validated['ids'],$params);
        return $this->response->success($result);
    }
}