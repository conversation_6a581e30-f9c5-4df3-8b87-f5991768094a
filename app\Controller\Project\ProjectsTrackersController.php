<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/7/4 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\ProjectsTrackersService;
use App\Request\Project\ProjectsWatchersRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("项目跟进成员")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProjectsTrackersController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ProjectsTrackersService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("添加跟进成员")
     */
    public function doWatchers()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }
}