<?php


namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

/**
 * 数据库连接枚举
 */
class DataBaseCode extends AbstractConstants
{
    /**
     * @Message('中文论坛')
     */
    const STATIONPC_BBSCN = 'stationpc_bbscn';

    /**
     * @Message('英文论坛')
     */
    const STATIONPC_BBSEN = 'stationpc_bbsen';

    /**
     * @Message('StationPC管理后台 CN')
     */
    CONST STATIONPC_MANAGERCN = 'stationpc_managercn';

    /**
     * @Message('StationPC管理后台 EN')
     */
    CONST STATIONPC_MANAGEREN = 'stationpc_manageren';

    /**
     * @Message('Tchip Redmine')
     */
    CONST TCHIP_REDMINE = 'tchip_redmine';

    /**
     * @Message('Tchip Sale')
     */
    CONST TCHIP_SALE = 'tchip_sale';

    /**
     * @Message('Tchip OA')
     */
    CONST TCHIP_OA = 'tchip_oa';

    /**
     * @Message('Tchip bbs')
     */
    const TCHIP_BI_BBS = 'tchip_bbs';

}