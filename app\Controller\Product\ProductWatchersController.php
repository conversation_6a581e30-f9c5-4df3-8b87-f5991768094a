<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/26 下午5:24
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Product;

use App\Controller\BaseController;
use App\Core\Services\Product\ProductWatcheService;
use App\Request\Product\ProductRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * 产品列表
 * @deprecated
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ProductWatchersController extends BaseController
{
    /**
     * @var ProductWatcheService
     * @Inject()
     */
    protected $service;


    public function doEditProductWatchers(ProductRequest $request)
    {
        $validated = $request->validated();
        $params = $this->request->all();
        if (isset($params['product_id'])) {
            unset($params['product_id']);
        }
        $result = $this->service->doEditProductWatchers($validated['product_id'], $params);
        return $this->response->success($result);
    }
}