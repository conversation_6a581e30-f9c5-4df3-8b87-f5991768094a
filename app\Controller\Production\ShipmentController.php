<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\Shipment\ShipmentService;
use App\Core\Services\TchipSale\OutStockQrcodeService;
use App\Core\Services\TchipSale\SaleQrcodeService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("订单出库")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ShipmentController extends BaseController
{
    /**
     * @var ShipmentService
     * @Inject()
     */
    protected $service;

    /**
     * 获取二维码的销售系统信息
     * @return ResponseInterface
     */
    public function qrcodeData()
    {
        $qrcodeId = $this->request->input('qrcode_id',0);
        $result = $this->service->getQrcodeOverView($qrcodeId);
        return $this->response->success($result);
    }

    public function getQrcodeList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getQrcodeList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }


}
