<?php

declare(strict_types=1);

namespace App\Command;

use App\Constants\StatusCode;
use App\Exception\AppException;
use App\Model\Marketing\MarketingContent;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;

/**
 * 命令行-执行SQL语句
 * @Command
 */
class Sql extends HyperfCommand
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var MarketingContent
     */
    protected $marketingContentModel;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;

        parent::__construct('sql:command');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('执行SQL指令');

        // $this->addUsage('--path 文件地址');
        // $this->addArgument('path', InputArgument::REQUIRED, '文件地址');
        // $this->addOption('path', 'path', InputOption::VALUE_REQUIRED);
        $this->addOption('command', 'cmd', InputOption::VALUE_REQUIRED);
    }

    public function handle()
    {
        $cmd = $this->input->getOption('command');
        $this->$cmd();


        // $items = Db::table('marketing_content')->distinct()->select('channel', 'platform')->get();
        // $this->output->progressStart(count($items));
        // foreach ($items->toArray() as $item) {
        //
        //     $platform = Db::table('marketing_platform')->where('name', $item->platform)->first();
        //     // var_dump($item, $platform);
        //
        //     Db::table('marketing_channel')->insert([
        //         'platform_id' => $platform->id,
        //         'platform_name' => $platform->name,
        //         'name' => $item->channel,
        //         'user_count' => Db::table('marketing_content')->where('channel', $item->channel)
        //             ->where('platform', $item->platform)->value('user_count')
        //     ]);
        //
        //     $this->output->progressAdvance();
        // }
        // $this->output->progressFinish();



        // var_dump($this->input->getOption('path'));
        // // $result = $this->ask('是否?(y/n)','y');
        // // var_dump($result);
        // $this->alert('successful');
        // $this->warn('warning');
        // $this->output->progressStart(100);
        // $this->output->progressAdvance(10);
        // $this->output->progressFinish();
        // $this->output->table([1,2], [[1,2]]);
        // $this->line('Hello Hyperf!', 'info');
    }

    public function restContent()
    {
        $items = Db::table('marketing_content')->get();
        $this->output->progressStart(count($items));
        foreach ($items as $item) {
            $platformId = Db::table('marketing_platform')->where('name', $item->platform)->value('id');
            $channelId = Db::table('marketing_channel')->where('name', $item->channel)
                ->where('platform_id', $platformId)->value('id');
            Db::table('marketing_content')->where('id', $item->id)->update(['platform_id'=>$platformId, 'channel_id'=>$channelId]);
            $this->output->progressAdvance();
        }
        $this->output->progressFinish();
    }
}
