<?php

namespace App\Controller\Production\Assemble;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\AssembleOrder\AssembleOrderIqcService;
use App\Core\Services\AssembleOrder\AssembleOrderShipService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("组装订单发货")
 * @AutoController(prefix="/production/assemble_order_ship")
 * @Middleware(AuthMiddleware::class)
 */
class AssembleOrderShipController extends BaseController
{
    /**
     * @Inject()
     * @var AssembleOrderShipService
     */
    protected $service;

}