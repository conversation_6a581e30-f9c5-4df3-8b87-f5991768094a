<?php

namespace App\Controller\Production;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\ExceptionRecord\ExceptionRecordReplyService;
use App\Core\Services\ExceptionRecord\ExceptionRecordService;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("异常跟踪")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ExceptionRecordController extends BaseController
{
    /**
     * @Inject()
     * @var ExceptionRecordService
     */
    protected $service;

    public function getRelateList()
    {
        list($filter, $op, $sort, $order) = $this->getParams();
        $result = $this->service->getRelateList($filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("新增编辑回复")
     * @Middleware(MenuMiddleware::class)
     */
    public function doEditReply()
    {
        $id = (int)$this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = make(ExceptionRecordReplyService::class)->doEdit($id, $params);
        return $this->response->success($result);
    }

}